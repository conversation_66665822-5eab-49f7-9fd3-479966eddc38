# This file may be used to create an environment using:
# $ conda create --name <env> --file <this file>
# platform: linux-64
_libgcc_mutex=0.1=main
_openmp_mutex=5.1=1_gnu
absl-py=1.4.0=pypi_0
accelerate=0.22.0=pypi_0
addict=2.4.0=pypi_0
aiofiles=23.2.1=pypi_0
aiohttp=3.8.5=pypi_0
aiosignal=1.3.1=pypi_0
aliyun-python-sdk-core=2.14.0=pypi_0
aliyun-python-sdk-kms=2.16.2=pypi_0
altair=5.1.1=pypi_0
annotated-types=0.5.0=pypi_0
anyio=3.7.1=pypi_0
appdirs=1.4.4=pypi_0
argparse=1.4.0=pypi_0
async-timeout=4.0.3=pypi_0
attrs=23.1.0=pypi_0
audeer=1.20.1=pypi_0
audformat=1.0.1=pypi_0
audinterface=1.0.3=pypi_0
audiofile=1.2.1=pypi_0
audioread=3.0.0=pypi_0
audmath=1.2.1=pypi_0
audobject=0.7.9=pypi_0
audresample=1.3.2=pypi_0
audtorch=0.6.4=pypi_0
beautifulsoup4=4.12.2=pypi_0
bitsandbytes=0.41.1=pypi_0
ca-certificates=2023.05.30=h06a4308_0
cachetools=5.3.1=pypi_0
certifi=2022.12.7=pypi_0
cffi=1.15.1=pypi_0
charset-normalizer=2.1.1=pypi_0
click=8.1.3=pypi_0
cmake=3.25.0=pypi_0
cmu-multimodal-sdk=0.0.6=pypi_0
colorama=0.4.6=pypi_0
contourpy=1.1.0=pypi_0
crcmod=1.7=pypi_0
cryptography=41.0.5=pypi_0
cycler=0.11.0=pypi_0
dadaptation=3.1=pypi_0
datasets=2.13.0=pypi_0
decorator=5.1.1=pypi_0
dill=0.3.6=pypi_0
easydict=1.10=pypi_0
einops=0.6.1=pypi_0
et-xmlfile=1.1.0=pypi_0
exceptiongroup=1.1.1=pypi_0
fairscale=0.4.13=pypi_0
fastapi=0.103.1=pypi_0
ffmpeg-python=0.2.0=pypi_0
ffmpy=0.3.1=pypi_0
filelock=3.9.0=pypi_0
fire=0.5.0=pypi_0
flatbuffers=23.5.26=pypi_0
fonttools=4.40.0=pypi_0
frozenlist=1.4.0=pypi_0
fsspec=2023.6.0=pypi_0
future=0.18.3=pypi_0
gast=0.5.4=pypi_0
gdown=4.7.1=pypi_0
gensim=4.3.1=pypi_0
gradio=3.43.2=pypi_0
gradio-client=0.5.0=pypi_0
h11=0.14.0=pypi_0
h5py=3.9.0=pypi_0
hiq-python=1.1.12=pypi_0
httpcore=0.17.3=pypi_0
httpx=0.24.1=pypi_0
huggingface-hub=0.26.2=pypi_0
idna=3.4=pypi_0
importlib-metadata=6.7.0=pypi_0
importlib-resources=5.12.0=pypi_0
iniconfig=2.0.0=pypi_0
iso-639=0.4.5=pypi_0
iso3166=2.1.1=pypi_0
jieba=0.42.1=pypi_0
jinja2=3.1.2=pypi_0
jmespath=0.10.0=pypi_0
joblib=1.2.0=pypi_0
jsonschema=4.19.0=pypi_0
jsonschema-specifications=2023.7.1=pypi_0
kiwisolver=1.4.4=pypi_0
latex2mathml=3.75.2=pypi_0
lazy-loader=0.2=pypi_0
libedit=3.1.20221030=h5eee18b_0
libffi=3.2.1=hf484d3e_1007
libgcc-ng=11.2.0=h1234567_1
libgomp=11.2.0=h1234567_1
librosa=0.10.0.post2=pypi_0
libstdcxx-ng=11.2.0=h1234567_1
lit=15.0.7=pypi_0
llvmlite=0.40.1rc1=pypi_0
markdown=3.4.4=pypi_0
markdown-it-py=3.0.0=pypi_0
markupsafe=2.1.2=pypi_0
matplotlib=3.7.1=pypi_0
mdtex2html=1.2.0=pypi_0
mdurl=0.1.2=pypi_0
mediapipe=0.10.1=pypi_0
mmengine=0.8.4=pypi_0
mmsa=2.2.1=pypi_0
mmsa-fet=0.3.1=pypi_0
modelscope=1.9.4=pypi_0
mpmath=1.2.1=pypi_0
msgpack=1.0.5=pypi_0
multidict=6.0.4=pypi_0
multiprocess=0.70.14=pypi_0
mypy-extensions=1.0.0=pypi_0
ncurses=6.4=h6a678d5_0
networkx=3.0=pypi_0
numba=0.57.0=pypi_0
numpy=1.24.1=pypi_0
nvidia-ml-py=12.535.133=pypi_0
nvidia-ml-py3=7.352.0=pypi_0
nvitop=1.3.1=pypi_0
opencv-contrib-python=********=pypi_0
opencv-python=********=pypi_0
openpyxl=3.1.2=pypi_0
opensmile=2.4.2=pypi_0
openssl=1.1.1u=h7f8727e_0
orjson=3.9.7=pypi_0
oss2=2.18.3=pypi_0
oyaml=1.0=pypi_0
packaging=23.1=pypi_0
pandas=2.0.2=pypi_0
peft=0.5.0=pypi_0
pillow=9.3.0=pypi_0
pip=23.1.2=py38h06a4308_0
pkgutil-resolve-name=1.3.10=pypi_0
platformdirs=3.10.0=pypi_0
pluggy=1.0.0=pypi_0
pooch=1.6.0=pypi_0
protobuf=3.20.3=pypi_0
psutil=5.9.5=pypi_0
py-itree=0.0.19=pypi_0
pyarrow=13.0.0=pypi_0
pycparser=2.21=pypi_0
pycryptodome=3.19.0=pypi_0
pydantic=2.3.0=pypi_0
pydantic-core=2.6.3=pypi_0
pydub=0.25.1=pypi_0
pygments=2.16.1=pypi_0
pyllama=0.0.9=pypi_0
pyparsing=3.1.0=pypi_0
pyre-extensions=0.0.29=pypi_0
pysocks=1.7.1=pypi_0
pytest=7.3.2=pypi_0
python=3.8.0=h0371630_2
python-dateutil=2.8.2=pypi_0
python-multipart=0.0.6=pypi_0
python-speech-features=0.6=pypi_0
pytorch-metric-learning=0.9.99=pypi_0
pytz=2023.3=pypi_0
pyyaml=6.0=pypi_0
randluck=0.0.1=pypi_0
readline=7.0=h7b6447c_5
referencing=0.30.2=pypi_0
regex=2023.6.3=pypi_0
requests=2.28.1=pypi_0
resampy=0.4.2=pypi_0
rich=13.5.2=pypi_0
rpds-py=0.10.2=pypi_0
safetensors=0.3.1=pypi_0
scenedetect=0.6.1=pypi_0
scikit-learn=1.2.2=pypi_0
scipy=1.10.1=pypi_0
seaborn=0.12.2=pypi_0
semantic-version=2.10.0=pypi_0
sentencepiece=0.1.97=pypi_0
setuptools=67.8.0=py38h06a4308_0
simplejson=3.19.2=pypi_0
six=1.16.0=pypi_0
sklearn=0.0.post5=pypi_0
smart-open=6.3.0=pypi_0
sniffio=1.3.0=pypi_0
sortedcontainers=2.4.0=pypi_0
sounddevice=0.4.6=pypi_0
soundfile=0.12.1=pypi_0
soupsieve=2.4.1=pypi_0
soxr=0.3.5=pypi_0
sqlite=3.33.0=h62c20be_0
starlette=0.27.0=pypi_0
sxtwl=2.0.6=pypi_0
sympy=1.11.1=pypi_0
tabulate=0.9.0=pypi_0
termcolor=2.3.0=pypi_0
threadpoolctl=3.1.0=pypi_0
tiktoken=0.4.0=pypi_0
tk=8.6.12=h1ccaba5_0
tokenizers=0.13.3=pypi_0
tomli=2.0.1=pypi_0
toolz=0.12.0=pypi_0
torch=2.0.1+cu118=pypi_0
torchaudio=2.0.2+cu118=pypi_0
torchsummary=1.5.1=pypi_0
torchvision=0.15.2+cu118=pypi_0
tqdm=4.65.0=pypi_0
transformers=4.32.1=pypi_0
transformers-stream-generator=0.0.4=pypi_0
triton=2.0.0=pypi_0
typing-extensions=4.7.1=pypi_0
typing-inspect=0.9.0=pypi_0
tzdata=2023.3=pypi_0
urllib3=1.26.13=pypi_0
utils=1.0.1=pypi_0
uvicorn=0.23.2=pypi_0
validators=0.20.0=pypi_0
websockets=11.0.3=pypi_0
wheel=0.38.4=py38h06a4308_0
xformers=0.0.20=pypi_0
xtuner=0.1.0=pypi_0
xxhash=3.3.0=pypi_0
xz=5.4.2=h5eee18b_0
yapf=0.40.1=pypi_0
yarl=1.9.2=pypi_0
zipp=3.15.0=pypi_0
zlib=1.2.13=h5eee18b_0

import torch
import torch.nn as nn
import torch.nn.functional as F
from trains.subNets import BertTextEncoder
from trains.subNets.transformers_encoder.transformer import TransformerEncoder
from trains.singleTask.model.router import router
from mamba_modal.mamba_block import MambaBlock

# =================================================================================
# CoSSM and its dependencies
# =================================================================================

class MMCNNEncoderLayer(nn.Module):
    def __init__(self, input_size, output_size, dropout=0.1, kernel_size=3):
        super().__init__()
        self.cnn1 = nn.Conv1d(in_channels=input_size, out_channels=output_size, kernel_size=kernel_size, padding=(kernel_size - 1) // 2)
        self.cnn2 = nn.Conv1d(in_channels=input_size, out_channels=output_size, kernel_size=kernel_size, padding=(kernel_size - 1) // 2)
        self.dropout = nn.Dropout(dropout)
        self.activation = nn.ReLU()

    def forward(self, x1, x2):
        out1 = self.activation(self.cnn1(x1))
        out2 = self.activation(self.cnn2(x2))
        return self.dropout(out1), self.dropout(out2)

class MMMambaEncoderLayer(nn.Module):
    def __init__(self, d_model, mamba_config):
        super().__init__()
        self.mamba1 = MambaBlock(embed_dim=d_model, num_layers=1, **mamba_config)
        self.mamba2 = MambaBlock(embed_dim=d_model, num_layers=1, **mamba_config)

    def forward(self, x1, x2):
        out1 = self.mamba1(x1.permute(1, 0, 2))
        out2 = self.mamba2(x2.permute(1, 0, 2))
        if isinstance(out1, tuple): out1 = out1[0]
        if isinstance(out2, tuple): out2 = out2[0]
        return out1.permute(1, 0, 2), out2.permute(1, 0, 2)

class CoSSM(nn.Module):
    def __init__(self, num_layers, input_size, output_sizes, d_ffn, mamba_config):
        super().__init__()
        cnn_list, mamba_list = [], []
        for i in range(num_layers):
            current_input_size = input_size if i == 0 else output_sizes[i-1]
            current_output_size = output_sizes[i]
            cnn_list.append(MMCNNEncoderLayer(input_size=current_input_size, output_size=current_output_size))
            mamba_list.append(MMMambaEncoderLayer(d_model=current_output_size, mamba_config=mamba_config))
        self.mamba_layers = torch.nn.ModuleList(mamba_list)
        self.cnn_layers = torch.nn.ModuleList(cnn_list)

    def forward(self, x1, x2):
        out1, out2 = x1, x2
        for cnn_layer, mamba_layer in zip(self.cnn_layers, self.mamba_layers):
            out1, out2 = cnn_layer(out1.permute(0,2,1), out2.permute(0,2,1))
            out1, out2 = out1.permute(0,2,1), out2.permute(0,2,1)
            out1, out2 = mamba_layer(out1, out2)
        return out1, out2

# =================================================================================
# Core Components for EMOE with CoSSM
# =================================================================================

class ModalityExpert(nn.Module):
    def __init__(self, feature_dim, num_layers):
        super().__init__()
        self.processor = MambaBlock(embed_dim=feature_dim, num_layers=num_layers)
        self.confidence_estimator = nn.Linear(feature_dim, 1)
        self.output_projection = nn.Linear(feature_dim, feature_dim)
    
    def forward(self, x):
        features_seq = self.processor(x)
        if type(features_seq) == tuple:
            features_seq = features_seq[0]
        
        features_pooled = features_seq[-1]
        confidence = torch.sigmoid(self.confidence_estimator(features_pooled))
        output_pooled = self.output_projection(features_pooled)
        return output_pooled, confidence, features_seq

class DynamicExpertCollaboration(nn.Module):
    def __init__(self, input_dim, feature_dim, num_experts=3):
        super().__init__()
        self.router = nn.Sequential(
            nn.Linear(input_dim, 128), nn.ReLU(),
            nn.Linear(128, 64), nn.ReLU(),
            nn.Linear(64, num_experts)
        )
        
    def forward(self, input_features, expert_features):
        batch_size = expert_features[0].shape[0]
        if len(input_features.shape) > 2:
            input_features = input_features.reshape(batch_size, -1)
        logits = self.router(input_features)
        weights = F.softmax(logits, dim=1)
        expert_outputs = torch.stack(expert_features, dim=1)
        weighted_sum = (expert_outputs * weights.unsqueeze(-1)).sum(dim=1)
        return weighted_sum, weights

class DMOE(nn.Module):
    """
    DMOE 模型的核心实现。
    它整合了三个模态（文本、音频、视频）的输入，并通过一个动态的、基于专家混合（MoE）的机制来融合它们。
    该模型包含两个关键部分：
    1. Mixture of Modality Experts (MoME): 为每个样本动态计算模态权重。
    2. Unimodal Distillation (UD): 利用单模态的预测能力来指导多模态的学习。
    """
    def __init__(self, args):
        """
        模型初始化。
        参数:
            args (Namespace): 包含所有模型配置和超参数的命名空间。
        """
        super(DMOE, self).__init__()
        # 1. 初始化文本编码器 (可选，使用BERT)
        if args.use_bert:
            self.text_model = BertTextEncoder(use_finetune=args.use_finetune, transformers=args.transformers,
                                              pretrained=args.pretrained)
        self.use_bert = args.use_bert

        # 2. 从args中解包和设置模型参数
        dst_feature_dims, nheads = args.dst_feature_dim_nheads # 目标特征维度和注意力头数
        
        # 根据数据集名称和是否对齐，设置三个模态的序列长度
        if args.dataset_name == 'mosi':
            if args.need_data_aligned:
                self.len_l, self.len_v, self.len_a = 50, 50, 50
            else: # 未对齐的数据有不同的序列长度
                self.len_l, self.len_v, self.len_a = 50, 500, 375
        if args.dataset_name == 'mosei':
            if args.need_data_aligned:
                self.len_l, self.len_v, self.len_a = 50, 50, 50
            else:
                self.len_l, self.len_v, self.len_a = 50, 500, 500
        
        self.aligned = args.need_data_aligned # 是否对齐的标志
        self.orig_d_l, self.orig_d_a, self.orig_d_v = args.feature_dims # 原始特征维度
        self.d_l = self.d_a = self.d_v = dst_feature_dims # 变换后统一的特征维度
        self.num_heads = nheads # Transformer中的多头注意力头数
        self.layers = args.nlevels # Transformer的层数
        self.attn_dropout = args.attn_dropout # 通用注意力dropout
        self.attn_dropout_a = args.attn_dropout_a # 音频专用注意力dropout
        self.attn_dropout_v = args.attn_dropout_v # 视频专用注意力dropout
        self.relu_dropout = args.relu_dropout # ReLU激活后的dropout
        self.embed_dropout = args.embed_dropout # 输入嵌入的dropout
        self.res_dropout = args.res_dropout # 残差连接的dropout
        self.output_dropout = args.output_dropout # 输出层的dropout
        self.text_dropout = args.text_dropout # 文本特征的dropout
        self.attn_mask = args.attn_mask # 是否使用注意力掩码
        self.fusion_method = args.fusion_method # 融合方法 ('sum' 或 'concat')
        output_dim = 1 # 回归任务的输出维度为1
        self.args = args

        # 3. 定义模态投影层 (使用1D卷积将不同维度的输入投影到统一维度)
        self.proj_l = nn.Conv1d(self.orig_d_l, self.d_l, kernel_size=args.conv1d_kernel_size_l, padding=0, bias=False)
        self.proj_a = nn.Conv1d(self.orig_d_a, self.d_a, kernel_size=args.conv1d_kernel_size_a, padding=0, bias=False)
        self.proj_v = nn.Conv1d(self.orig_d_v, self.d_v, kernel_size=args.conv1d_kernel_size_v, padding=0, bias=False)

        # 4. 定义编码器层 (在进入Transformer之前对特征进行初步编码)
        # 注意：这里所有模态共享同一个encoder_c，用于提取模态不变特征 (common features)
        self.encoder_c = nn.Conv1d(self.d_l, self.d_l, kernel_size=1, padding=0, bias=False)
        # 这几个编码器在当前实现中并未使用，可能为未来扩展保留
        self.encoder_l = nn.Conv1d(self.d_l, self.d_l, kernel_size=1, padding=0, bias=False)
        self.encoder_v = nn.Conv1d(self.d_v, self.d_v, kernel_size=1, padding=0, bias=False)
        self.encoder_a = nn.Conv1d(self.d_a, self.d_a, kernel_size=1, padding=0, bias=False)

        # 5. 为每个模态实例化独立的Transformer编码器
        self.self_attentions_l = self.get_network(self_type='l')
        self.self_attentions_v = self.get_network(self_type='v')
        self.self_attentions_a = self.get_network(self_type='a')

        # 6. 为每个单模态定义独立的预测头 (用于Unimodal Distillation)
        self.proj1_l = nn.Linear(self.d_l, self.d_l)
        self.proj2_l = nn.Linear(self.d_l, self.d_l)
        self.out_layer_l = nn.Linear(self.d_l, output_dim)
        self.proj1_v = nn.Linear(self.d_l, self.d_l)
        self.proj2_v = nn.Linear(self.d_l, self.d_l)
        self.out_layer_v = nn.Linear(self.d_l, output_dim)
        self.proj1_a = nn.Linear(self.d_l, self.d_l)
        self.proj2_a = nn.Linear(self.d_l, self.d_l)
        self.out_layer_a = nn.Linear(self.d_l, output_dim)

        # 7. 为融合后的多模态特征定义预测头
        if self.fusion_method == "sum": # 加权求和融合
            self.proj1_c = nn.Linear(self.d_l, self.d_l)
            self.proj2_c = nn.Linear(self.d_l, self.d_l)
            self.out_layer_c = nn.Linear(self.d_l, output_dim)
        elif self.fusion_method == "concat": # 加权拼接融合
            self.proj1_c = nn.Linear(self.d_l*3, self.d_l*3)
            self.proj2_c = nn.Linear(self.d_l*3, self.d_l*3)
            self.out_layer_c = nn.Linear(self.d_l*3, output_dim)

        # 8. 实例化路由网络 (Router Network)，论文MoME部分的核心
        # 输入维度是三个模态特征拼接后的总维度，输出维度是3（对应三个模态的权重）
        self.Router = router(self.orig_d_l * self.len_l + self.orig_d_a * self.len_l + self.orig_d_v * self.len_l, 3, self.args.temperature)
        
        # 9. 定义线性层，用于在数据未对齐时，将音视频的序列长度变换到与文本相同
        self.transfer_a_ali = nn.Linear(self.len_a, self.len_l)
        self.transfer_v_ali = nn.Linear(self.len_v, self.len_l)

        # Replaced Router with DynamicExpertCollaboration
        self.expert_collab = DynamicExpertCollaboration(
            input_dim=self.orig_d_l * self.len_l + self.orig_d_a * self.len_l + self.orig_d_v * self.len_l,
            feature_dim=self.d_l, num_experts=3
        )

        # Instantiate CoSSM for pairwise interaction (Post-Transformer)
        cossm_num_layers = 2
        cossm_output_sizes = [self.d_l] * cossm_num_layers
        cossm_d_ffn = 512
        cossm_mamba_config = {'dropout': 0.1, 'activation': 'gelu', 'use_residual': True}
        
        self.cossm_av = CoSSM(cossm_num_layers, self.d_l, cossm_output_sizes, cossm_d_ffn, mamba_config=cossm_mamba_config)
        self.cossm_ta = CoSSM(cossm_num_layers, self.d_l, cossm_output_sizes, cossm_d_ffn, mamba_config=cossm_mamba_config)
        self.cossm_tv = CoSSM(cossm_num_layers, self.d_l, cossm_output_sizes, cossm_d_ffn, mamba_config=cossm_mamba_config)

    def get_network(self, self_type='l', layers=-1):
        """
        一个工厂/辅助方法，用于根据指定的模态类型来创建和配置一个完整的Transformer编码器网络。
        这种设计模式使得为不同模态（文本、音频、视频）创建具有细微差别（如不同的dropout率）
        的专属编码器变得非常方便，同时又最大化地复用了代码。

        参数:
            self_type (str): 指定要创建的编码器所属的模态类型。
                             可选值为 'l' (语言), 'a' (音频), 'v' (视频)。
            layers (int): 可选参数，用于覆盖在args中设置的默认Transformer层数。
                          如果为-1，则使用默认层数。

        返回:
            TransformerEncoder: 一个配置完成的、准备好处理序列数据的Transformer编码器实例。
        """
        # 1. 根据模态类型，选择对应的特征维度和注意力dropout率。
        #    这允许我们为不同模态（例如，音频可能需要比文本更高的dropout率）进行精细化的超参数调整。
        if self_type == 'l':
            embed_dim, attn_dropout = self.d_l, self.attn_dropout
        elif self_type == 'a':
            embed_dim, attn_dropout = self.d_a, self.attn_dropout_a
        elif self_type == 'v':
            embed_dim, attn_dropout = self.d_v, self.attn_dropout_v
        else:
            raise ValueError("未知的网络类型")

        # 2. 实例化并返回一个TransformerEncoder。
        #    这里传入了所有从args中读取的、与Transformer相关的超参数。
        return TransformerEncoder(embed_dim=embed_dim,             # 特征维度，例如 128
                                  num_heads=self.num_heads,         # 多头注意力机制中的"头"数，例如 8
                                  layers=max(self.layers, layers),  # 编码器层数，例如 4
                                  attn_dropout=attn_dropout,        # 注意力权重矩阵的dropout率
                                  relu_dropout=self.relu_dropout,   # 全连接层中ReLU激活后的dropout率
                                  res_dropout=self.res_dropout,     # 残差连接上的dropout率
                                  embed_dropout=self.embed_dropout,# 输入嵌入（embedding）的dropout率
                                  attn_mask=self.attn_mask)         # 是否在自注意力计算中使用掩码

    def get_net(self, name):
        """一个简单的辅助函数，通过名字获取模型的一个层。"""
        return getattr(self, name)

    def forward(self, text, audio, video):
        """
        模型的前向传播逻辑。
        """
        # 1. 文本特征预处理 (如果使用BERT)
        if self.use_bert:
            text = self.text_model(text)
        
        # 将各模态输入转置为 (batch_size, feature_dim, seq_len) 以适应Conv1d
        x_l = F.dropout(text.transpose(1, 2), p=self.text_dropout, training=self.training)
        x_a = audio.transpose(1, 2)
        x_v = video.transpose(1, 2)

        # 2. 准备送入路由网络(Router)的输入
        # 如果数据未对齐，先通过线性层统一序列长度
        if not self.aligned:
            # permute 用于交换维度以匹配nn.Linear的输入要求
            audio_ = self.transfer_a_ali(audio.permute(0, 2, 1)).permute(0, 2, 1)
            video_ = self.transfer_v_ali(video.permute(0, 2, 1)).permute(0, 2, 1)
            # 拼接三个模态的原始特征
            m_i = torch.cat((text, video_, audio_), dim=2)
        else:
            m_i = torch.cat((text, video, audio), dim=2)
        
        # 3. 调用路由网络，计算模态权重 m_w
        m_w = self.Router(m_i)

        # 4. 特征投影：使用1D卷积将各模态输入投影到统一的特征维度
        proj_x_l = x_l if self.orig_d_l == self.d_l else self.proj_l(x_l)
        proj_x_a = x_a if self.orig_d_a == self.d_a else self.proj_a(x_a)
        proj_x_v = x_v if self.orig_d_v == self.d_v else self.proj_v(x_v)

        # 5. 共享编码器：所有模态通过一个共享的1D卷积层进行初步编码
        c_l = self.encoder_c(proj_x_l)
        c_v = self.encoder_c(proj_x_v)
        c_a = self.encoder_c(proj_x_a)

        # c_l, c_v, c_a 的维度是 [序列长度, 批次大小, 特征维度]
        # 这是为每个模态准备好的、进入各自专属Transformer前的数据
        c_l = c_l.permute(2, 0, 1)
        c_v = c_v.permute(2, 0, 1)
        c_a = c_a.permute(2, 0, 1)

        # 6. 模态专属Transformer编码与池化
        #    以下三个代码块的逻辑完全相同，分别为每个模态执行：
        #    a. 通过各自的Transformer编码器进行深度特征提取。
        #    b. 对Transformer的输出进行池化，将序列信息压缩成一个固定大小的向量。

        # --- 语言模态处理 ---
        # a. 将语言特征序列送入语言专用的Transformer编码器
        c_l_att_seq = self.self_attentions_l(c_l)
        # b. Transformer的返回值可能是一个元组(特征, 其他信息)，这里只取我们需要的特征部分
        if type(c_l_att_seq) == tuple:
            c_l_att_seq = c_l_att_seq[0]
        # c. 池化操作：取序列的最后一个时间步的输出作为整个序列的代表性特征。
        #    这是一种常见的将序列信息汇总为固定维度向量的方法。
        #    处理后，c_l_att 的维度从 [序列长度, 批次大小, 特征维度] 变为 [批次大小, 特征维度]。
        c_l_att = c_l_att_seq[-1]

        # --- 视频模态处理 ---
        c_v_att_seq = self.self_attentions_v(c_v)
        if type(c_v_att_seq) == tuple:
            c_v_att_seq = c_v_att_seq[0]
        c_v_att = c_v_att_seq[-1]
        
        # --- 音频模态处理 ---
        c_a_att_seq = self.self_attentions_a(c_a)
        if type(c_a_att_seq) == tuple:
            c_a_att_seq = c_a_att_seq[0]
        c_a_att = c_a_att_seq[-1]

        # 7. 计算单模态的预测输出 (logits)，用于 Unimodal Distillation
        #    这一步在跨模态交互之前进行，以保留纯净的单模态信息用于蒸馏。
        l_proj = self.proj2_l(
            F.dropout(F.relu(self.proj1_l(c_l_att), inplace=True), p=self.output_dropout,
                      training=self.training))
        l_proj += c_l_att # 残差连接
        logits_l = self.out_layer_l(l_proj)
        v_proj = self.proj2_v(
            F.dropout(F.relu(self.proj1_v(c_v_att), inplace=True), p=self.output_dropout,
                      training=self.training))
        v_proj += c_v_att
        logits_v = self.out_layer_v(v_proj)
        a_proj = self.proj2_a(
            F.dropout(F.relu(self.proj1_a(c_a_att), inplace=True), p=self.output_dropout,
                      training=self.training))
        a_proj += c_a_att
        logits_a = self.out_layer_a(a_proj)

        # 8. CoSSM 跨模态交互
        #    输入CoSSM前，需要将维度从 (Seq, Batch, Dim) 转换为 (Batch, Seq, Dim)
        c_l_seq_p = c_l_att_seq.permute(1, 0, 2)
        c_v_seq_p = c_v_att_seq.permute(1, 0, 2)
        c_a_seq_p = c_a_att_seq.permute(1, 0, 2)
        
        # 成对交互
        enhanced_a_av, enhanced_v_av = self.cossm_av(c_a_seq_p, c_v_seq_p)
        enhanced_t_ta, enhanced_a_ta = self.cossm_ta(c_l_seq_p, c_a_seq_p)
        enhanced_t_tv, enhanced_v_tv = self.cossm_tv(c_l_seq_p, c_v_seq_p)
        
        # 融合CoSSM的输出 (简单平均)
        c_l_final_seq = (enhanced_t_ta + enhanced_t_tv) / 2
        c_v_final_seq = (enhanced_v_av + enhanced_v_tv) / 2
        c_a_final_seq = (enhanced_a_av + enhanced_a_ta) / 2
        
        # 将维度转置回来并进行池化 (取最后一个时间步)
        c_l_fused_att = c_l_final_seq.permute(1, 0, 2)[-1]
        c_v_fused_att = c_v_final_seq.permute(1, 0, 2)[-1]
        c_a_fused_att = c_a_final_seq.permute(1, 0, 2)[-1]

        # 9. 动态特征融合 (Dynamic Fusion) - 使用CoSSM增强后的特征
        if self.fusion_method == "sum": 
            # 遍历batch中的每个样本，使用其专属的权重 m_w[i] 进行加权求和
            for i in range(m_w.shape[0]):
                c_f = c_l_fused_att[i] * m_w[i][0] + c_v_fused_att[i] * m_w[i][1] + c_a_fused_att[i] * m_w[i][2]
                if i == 0: c_fusion = c_f.unsqueeze(0)
                else: c_fusion = torch.cat([c_fusion, c_f.unsqueeze(0)], dim=0)   
        elif self.fusion_method == "concat":        
            # 遍历batch中的每个样本，进行加权拼接
            for i in range(m_w.shape[0]):
                c_f = torch.cat([c_l_fused_att[i] * m_w[i][0], c_v_fused_att[i] * m_w[i][1], c_a_fused_att[i] * m_w[i][2]], dim=0) * 3
                if i == 0: c_fusion = c_f.unsqueeze(0)
                else: c_fusion = torch.cat([c_fusion, c_f.unsqueeze(0)], dim=0)   

        # 10. 计算多模态融合后的预测输出
        c_proj = self.proj2_c(
            F.dropout(F.relu(self.proj1_c(c_fusion), inplace=True), p=self.output_dropout,
                      training=self.training))
        c_proj += c_fusion # 残差连接
        logits_c = self.out_layer_c(c_proj)

        # 11. 将所有需要用于计算损失的中间结果打包返回
        res = {
            'logits_c': logits_c,           # 多模态预测结果
            'logits_l': logits_l,           # 文本单模态预测结果
            'logits_v': logits_v,           # 视频单模态预测结果
            'logits_a': logits_a,           # 音频单模态预测结果
            'channel_weight': m_w,          # 路由网络输出的模态权重
            'c_proj': c_proj,               # 多模态最终特征
            'l_proj': l_proj,               # 文本单模态最终特征
            'v_proj': v_proj,               # 视频单模态最终特征
            'a_proj': a_proj,               # 音频单模态最终特征
            'c_fea': c_fusion,              # 融合后的特征(进入最后proj层之前)
        }

        # Replaced Router with DynamicExpertCollaboration
        # The fusion method 'sum' or 'concat' will now apply to the final projection head input
        if self.fusion_method == "concat":
             c_fusion = torch.cat([c_l_fused_att, c_v_fused_att, c_a_fused_att], dim=0)

        return res
import logging
import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
from torch import optim
from torch.optim.lr_scheduler import ReduceLROnPlateau
from tqdm import tqdm
from trains.utils import MetricsTop, dict_to_str, eva_imp, uni_distill, entropy_balance
logger = logging.getLogger('DEMO')
logger.setLevel(logging.INFO)
if not logger.handlers:
    ch = logging.StreamHandler()
    ch.setLevel(logging.INFO)
    formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
    ch.setFormatter(formatter)
    logger.addHandler(ch)
    
class DEMO():
    def __init__(self, args):
        self.args = args
        self.criterion = nn.L1Loss()
        # 对抗蒸馏使用的损失函数
        self.criterion_adv = nn.CrossEntropyLoss()
        self.metrics = MetricsTop(args.train_mode).getMetics(args.dataset_name)

    def do_train(self, model, dataloader, return_epoch_results=False):
        # 为主模型（生成器 G）和判别器 D 创建独立的优化器
        # 使用 getattr 安全地获取超参数，如果未定义则使用默认值
        lr_D = getattr(self.args, 'learning_rate_D', self.args.learning_rate)
        optimizer_G = optim.Adam(model.parameters(), lr=self.args.learning_rate)
        optimizer_D = optim.Adam(model.discriminator.parameters(), lr=lr_D)

        scheduler_G = ReduceLROnPlateau(optimizer_G, mode='min', factor=0.5, verbose=True, patience=self.args.patience)

        epochs, best_epoch = 0, 0
        if return_epoch_results:
            epoch_results = {
                'train': [],
                'valid': [],
                'test': []
            }
        min_or_max = 'min' if self.args.KeyEval in ['Loss'] else 'max'
        best_valid = 1e8 if min_or_max == 'min' else 0

        while True:
            epochs += 1
            y_pred, y_true = [], []
            model.train()
            train_loss = 0.0

            left_epochs = self.args.update_epochs
            with tqdm(dataloader['train']) as td:
                for batch_data in td:
                    if left_epochs == self.args.update_epochs:
                        # 在每个梯度累积周期开始时，只重置生成器的优化器
                        optimizer_G.zero_grad()
                    left_epochs -= 1
                    vision = batch_data['vision'].to(self.args.device)
                    audio = batch_data['audio'].to(self.args.device)
                    text = batch_data['text'].to(self.args.device)
                    labels = batch_data['labels']['M'].to(self.args.device)
                    labels = labels.view(-1, 1)

                    output = model(text, audio, vision)
                    # 修复：重新添加被误删的行，以收集用于计算训练集指标的预测和标签
                    y_pred.append(output['logits_c'].cpu())
                    y_true.append(labels.cpu())
                    w = output['channel_weight']
                    #print(w)

                    # ==================================================================
                    # 阶段一: 训练判别器 D (固定主模型 G)
                    # ==================================================================
                    optimizer_D.zero_grad()
                    
                    # 使用交互前的纯粹特征来训练判别器
                    l_proj, v_proj, a_proj = output['l_proj'].detach(), output['v_proj'].detach(), output['a_proj'].detach()
                    
                    # 判别器对纯粹特征进行预测
                    pred_l_pure = model.discriminator(l_proj)
                    pred_v_pure = model.discriminator(v_proj)
                    pred_a_pure = model.discriminator(a_proj)

                    # 创建真实标签
                    batch_size = l_proj.size(0)
                    labels_l = torch.zeros(batch_size, dtype=torch.long, device=self.args.device) # 文本标签: 0
                    labels_v = torch.ones(batch_size, dtype=torch.long, device=self.args.device)  # 视频标签: 1
                    labels_a = torch.ones(batch_size, dtype=torch.long, device=self.args.device) * 2 # 音频标签: 2

                    # 计算判别器损失
                    loss_D = self.criterion_adv(pred_l_pure, labels_l) + \
                             self.criterion_adv(pred_v_pure, labels_v) + \
                             self.criterion_adv(pred_a_pure, labels_a)
                    
                    loss_D.backward()
                    optimizer_D.step()

                    # ==================================================================
                    # 阶段二: 训练主模型 G (固定判别器 D)
                    # ==================================================================
                    # 计算主任务损失和原始的辅助损失
                    loss_task_l = self.criterion(output['logits_l'], labels)
                    loss_task_v = self.criterion(output['logits_v'], labels)
                    loss_task_a = self.criterion(output['logits_a'], labels)
                    loss_task_m = self.criterion(output['logits_c'], labels)

                    l_dist = eva_imp(output['logits_l'], labels)
                    a_dist = eva_imp(output['logits_a'], labels)
                    v_dist = eva_imp(output['logits_v'], labels)
                    dist = torch.zeros(l_dist.shape[0], 3).to(self.args.device)
                    for i,_ in enumerate(l_dist):
                        s = 1/(l_dist[i]+0.1) + 1/(v_dist[i]+0.1) + 1/(a_dist[i]+0.1)
                        dist[i][0] = (1/(l_dist[i]+0.1)) / s
                        dist[i][1] = (1/(v_dist[i]+0.1)) / s
                        dist[i][2] = (1/(a_dist[i]+0.1)) / s
                    loss_sim = torch.mean(torch.mean((dist.detach() - w) ** 2, dim=-1))
                    loss_ety = entropy_balance(w)

                    if self.args.fusion_method == "sum":
                        # 注意：这里我们使用交互后的特征来构建教师，实现方案一中的“增强型教师”
                        teacher_features = (output['l_proj_interacted'] * w[:,0].view(-1, 1) + 
                                            output['v_proj_interacted'] * w[:,1].view(-1, 1) + 
                                            output['a_proj_interacted'] * w[:,2].view(-1, 1)).detach()
                        loss_ud = uni_distill(output['c_proj'], teacher_features)
                    elif self.args.fusion_method == "concat":
                        teacher_features = torch.cat([output['l_proj_interacted'] * w[:,0].view(-1, 1),
                                                    output['v_proj_interacted'] * w[:,1].view(-1, 1),
                                                    output['a_proj_interacted'] * w[:,2].view(-1, 1)], dim=1).detach()
                        loss_ud = uni_distill(output['c_proj'], teacher_features)
                    
                    # --- 计算生成器的对抗性损失 loss_adv_G ---
                    # 目标是让判别器D的输出接近均匀分布 [1/3, 1/3, 1/3]
                    # 获取所有“混血”特征
                    l_proj_inter, v_proj_inter, a_proj_inter, c_proj = \
                        output['l_proj_interacted'], output['v_proj_interacted'], \
                        output['a_proj_interacted'], output['c_proj']
                    
                    # 判别器对混血特征进行预测
                    pred_l_inter = model.discriminator(l_proj_inter)
                    pred_v_inter = model.discriminator(v_proj_inter)
                    pred_a_inter = model.discriminator(a_proj_inter)
                    pred_c_proj  = model.discriminator(c_proj)

                    # 对抗损失的目标是让判别器无法识别来源，即预测的标签是错误的
                    # 一个简单有效的方法是，我们希望判别器将文本特征误判为视频(1)，视频误判为音频(2)，音频误判为文本(0)
                    # 这比KL散度更容易实现且效果相似
                    loss_adv_l = self.criterion_adv(pred_l_inter, labels_v) # 期望 l 被判成 v
                    loss_adv_v = self.criterion_adv(pred_v_inter, labels_a) # 期望 v 被判成 a
                    loss_adv_a = self.criterion_adv(pred_a_inter, labels_l) # 期望 a 被判成 l
                    # 对于最终融合特征，我们可以让它被误判为任何一个，或者平均其损失
                    loss_adv_c = (self.criterion_adv(pred_c_proj, labels_l) + \
                                  self.criterion_adv(pred_c_proj, labels_v) + \
                                  self.criterion_adv(pred_c_proj, labels_a)) / 3
                    
                    loss_adv_G = (loss_adv_l + loss_adv_v + loss_adv_a + loss_adv_c) / 4

                    # --- 计算最终的总损失 ---
                    # 使用 getattr 安全地获取损失权重，如果未定义则使用默认值
                    lambda_aux = getattr(self.args, 'lambda_aux', 0.1)
                    lambda_adv = getattr(self.args, 'lambda_adv', 0.1)
                    loss = loss_task_m + \
                           (loss_task_l + loss_task_v + loss_task_a)/3 + \
                           lambda_aux * (loss_ety + 0.1*loss_sim + loss_ud) + \
                           lambda_adv * loss_adv_G

                    loss.backward()
                    train_loss += loss.item()

                    if not left_epochs:
                        optimizer_G.step()
                        left_epochs = self.args.update_epochs
                if not left_epochs:
                    optimizer_G.step()

            train_loss = train_loss / len(dataloader['train'])
            pred, true = torch.cat(y_pred), torch.cat(y_true)
            train_results = self.metrics(pred, true)
            logger.info(
                f">> Epoch: {epochs} "
                f"TRAIN-({self.args.model_name}) [{epochs - best_epoch}/{epochs}/{self.args.cur_seed}] "
                f">> total_loss: {round(train_loss, 4)} "
                f"{dict_to_str(train_results)}"
            )
            val_results = self.do_test(model, dataloader['valid'], mode="VAL")
            test_results = self.do_test(model, dataloader['test'], mode="TEST")
            cur_valid = val_results[self.args.KeyEval]
            scheduler_G.step(val_results['Loss'])
            torch.save(model.state_dict(), './pt/' + str(epochs) + '.pth')
            isBetter = cur_valid <= (best_valid - 1e-6) if min_or_max == 'min' else cur_valid >= (best_valid + 1e-6)
            if isBetter:
                best_valid, best_epoch = cur_valid, epochs
                model_save_path = './pt/emoe.pth'
                torch.save(model.state_dict(), model_save_path)

            if return_epoch_results:
                train_results["Loss"] = train_loss
                epoch_results['train'].append(train_results)
                epoch_results['valid'].append(val_results)
                test_results = self.do_test(model, dataloader['test'], mode="TEST")
                epoch_results['test'].append(test_results)
            if epochs - best_epoch >= self.args.early_stop:
                return epoch_results if return_epoch_results else None

    def do_test(self, model, dataloader, mode="VAL", return_sample_results=False, f=0):
        model.eval()
        y_pred, y_true = [], []
        weight, ability = [], []
        c_fea = []

        eval_loss = 0.0
        if return_sample_results:
            ids, sample_results = [], []
            all_labels = []
            features = {
                "Feature_t": [],
                "Feature_a": [],
                "Feature_v": [],
                "Feature_f": [],
            }

        with torch.no_grad():
            with tqdm(dataloader) as td:
                for batch_data in td:
                    vision = batch_data['vision'].to(self.args.device)
                    audio = batch_data['audio'].to(self.args.device)
                    text = batch_data['text'].to(self.args.device)
                    labels = batch_data['labels']['M'].to(self.args.device)
                    labels = labels.view(-1, 1)
                    output = model(text, audio, vision)

                    loss = self.criterion(output['logits_c'], labels)
                    eval_loss += loss.item()
                    y_pred.append(output['logits_c'].cpu())
                    y_true.append(labels.cpu())
                    
        eval_loss = eval_loss / len(dataloader)
        pred, true = torch.cat(y_pred), torch.cat(y_true)
        eval_results = self.metrics(pred, true)
        eval_results["Loss"] = round(eval_loss, 4)
        logger.info(f"{mode}-({self.args.model_name}) >> {dict_to_str(eval_results)}")
        
        if return_sample_results:
            eval_results["Ids"] = ids
            eval_results["SResults"] = sample_results
            for k in features.keys():
                features[k] = np.concatenate(features[k], axis=0)
            eval_results['Features'] = features
            eval_results['Labels'] = all_labels

        return eval_results
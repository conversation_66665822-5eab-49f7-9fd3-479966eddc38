import torch
import torch.nn as nn

class MambaBlock(nn.Module):
    """
    MHFE实现
    MambaBlock: 多模态特征提取的状态空间建模模块（可类比MHFE），接口与nn.Module兼容。
    支持多层堆叠、可选激活、残差，适合多模态时序特征提取。
    """
    def __init__(self, embed_dim, num_layers=1, dropout=0.1, activation='gelu', use_residual=True):
        super().__init__()
        self.use_residual = use_residual
        # 选择激活函数
        if activation == 'gelu':
            act = nn.GELU()
        elif activation == 'relu':
            act = nn.ReLU()
        else:
            raise ValueError('Unsupported activation')
        # 堆叠多层MambaBlock，每层包含归一化、线性变换、激活、dropout
        self.layers = nn.ModuleList([
            nn.Sequential(
                nn.LayerNorm(embed_dim),
                nn.Linear(embed_dim, embed_dim),
                act,
                nn.Dropout(dropout),
                nn.Linear(embed_dim, embed_dim),
                nn.Dropout(dropout)
            ) for _ in range(num_layers)
        ])

    def forward(self, x):
        """
        x: (batch, seq_len, embed_dim) 输入为三维张量
        返回：同形状的特征张量
        """
        for layer in self.layers:
            if self.use_residual:
                x = x + layer(x)  # 残差连接
            else:
                x = layer(x)
        #print("---------mmoe---------")
        return x

# 注：此为可扩展版MambaBlock。实际可替换为MASCF_implement或官方Mamba的S6Block/Mamba实现。 
import torch
import torch.nn as nn
import torch.nn.functional as F
from mamba_modal.mamba_block import MambaBlock

class MOE_Router(nn.Module):
    """
    MOE路由网络：根据多模态特征动态分配权重，实现自适应融合。
    输入：拼接后的多模态特征（batch, total_dim）
    输出：每个样本的模态权重（batch, 3）
    """
    def __init__(self, input_dim, temperature=1.0):
        super().__init__()
        self.fc = nn.Sequential(
            nn.Linear(input_dim, 64),
            nn.ReLU(),
            nn.Linear(64, 3)
        )
        self.temperature = temperature
    def forward(self, x):
        w = self.fc(x) / self.temperature
        return F.softmax(w, dim=-1)

class EMOE_Mamba(nn.Module):
    """
    EMOE_Mamba: MHFE（MambaBlock）+MOE动态权重分配的多模态情感识别模型。
    结构与原EMOE兼容，便于直接替换。
    """
    def __init__(self, args):
        super().__init__()
        # 1. 特征投影层，将原始特征统一到指定维度
        self.proj_l = nn.Linear(args.orig_d_l, args.d_l)
        self.proj_a = nn.Linear(args.orig_d_a, args.d_a)
        self.proj_v = nn.Linear(args.orig_d_v, args.d_v)
        # 2. MHFE模块（MambaBlock）分别提取三模态时序特征
        self.mamba_l = MambaBlock(args.d_l, args.nlevels)
        self.mamba_a = MambaBlock(args.d_a, args.nlevels)
        self.mamba_v = MambaBlock(args.d_v, args.nlevels)
        # 3. 池化层（取最后一个时间步）
        self.pool = lambda x: x[:, -1, :]
        # 4. MOE路由网络，输入为三模态拼接特征
        router_in_dim = args.d_l + args.d_a + args.d_v
        self.router = MOE_Router(router_in_dim, temperature=getattr(args, 'temperature', 1.0))
        # 5. 融合后输出头
        self.fusion_dropout = nn.Dropout(getattr(args, 'fusion_dropout', 0.2))
        self.out_proj = nn.Sequential(
            nn.Linear(args.d_l, args.d_l),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(args.d_l, args.output_dim)
        )

    def forward(self, text, audio, video):
        """
        text, audio, video: (batch, seq_len, orig_feature_dim)
        返回：情感预测结果（batch, output_dim）
        """
        # 1. 特征投影
        text = self.proj_l(text)
        audio = self.proj_a(audio)
        video = self.proj_v(video)
        # 2. MHFE（MambaBlock）编码
        l_feat = self.mamba_l(text)
        a_feat = self.mamba_a(audio)
        v_feat = self.mamba_v(video)
        # 3. 池化（取最后一个时间步）
        l_vec = self.pool(l_feat)
        a_vec = self.pool(a_feat)
        v_vec = self.pool(v_feat)
        # 4. 拼接三模态特征，输入MOE路由网络
        moe_in = torch.cat([l_vec, a_vec, v_vec], dim=-1)
        weights = self.router(moe_in)  # (batch, 3)
        # 5. 用MOE权重加权融合三模态特征（加权和）
        fused = weights[:, 0:1] * l_vec + weights[:, 1:2] * a_vec + weights[:, 2:3] * v_vec
        fused = self.fusion_dropout(fused)
        # 6. 输出头
        out = self.out_proj(fused)
        return out, weights

# 注：如需支持加权拼接、单模态分支输出等，可进一步扩展。 
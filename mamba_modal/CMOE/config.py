import argparse

def get_expert_collaboration_config():
    """
    获取专家合作模型的默认配置
    """
    parser = argparse.ArgumentParser()
    
    # 模型基本参数
    parser.add_argument('--model_name', type=str, default='ExpertCollaboration', help='模型名称')
    parser.add_argument('--orig_d_l', type=int, default=768, help='文本特征维度')
    parser.add_argument('--orig_d_a', type=int, default=74, help='音频特征维度')
    parser.add_argument('--orig_d_v', type=int, default=35, help='视觉特征维度')
    parser.add_argument('--d_l', type=int, default=128, help='文本特征投影维度')
    parser.add_argument('--d_a', type=int, default=128, help='音频特征投影维度')
    parser.add_argument('--d_v', type=int, default=128, help='视觉特征投影维度')
    parser.add_argument('--output_dim', type=int, default=1, help='输出维度')
    
    # Mamba编码器参数
    parser.add_argument('--nlevels', type=int, default=4, help='Mamba层数')
    parser.add_argument('--use_mamba_encoder', type=bool, default=True, help='是否使用Mamba编码器')
    
    # 专家参数
    parser.add_argument('--expert_hidden_dim', type=int, default=128, help='专家隐藏层维度')
    parser.add_argument('--expert_output_dim', type=int, default=64, help='专家输出维度')
    parser.add_argument('--use_mamba_expert', type=bool, default=False, help='是否使用Mamba作为专家')
    parser.add_argument('--fusion_dropout', type=float, default=0.2, help='融合层dropout率')
    
    # 专家权重参数
    parser.add_argument('--class_specific_weights', type=bool, default=False, help='是否使用类别特定权重')
    parser.add_argument('--use_dynamic_weights', type=bool, default=False, help='是否使用动态权重')
    parser.add_argument('--num_classes', type=int, default=1, help='类别数量（用于类别特定权重）')
    
    # 训练参数
    parser.add_argument('--learning_rate', type=float, default=1e-4, help='学习率')
    parser.add_argument('--patience', type=int, default=5, help='学习率降低的耐心值')
    parser.add_argument('--early_stop', type=int, default=15, help='早停的耐心值')
    parser.add_argument('--update_epochs', type=int, default=1, help='更新周期')
    parser.add_argument('--batch_size', type=int, default=32, help='批次大小')
    parser.add_argument('--epochs', type=int, default=100, help='最大训练轮数')
    parser.add_argument('--cur_seed', type=int, default=42, help='随机种子')
    parser.add_argument('--train_mode', type=str, default='regression', help='训练模式：regression或classification')
    parser.add_argument('--dataset_name', type=str, default='mosi', help='数据集名称')
    parser.add_argument('--KeyEval', type=str, default='Loss', help='评估指标')
    
    # 蒸馏参数
    parser.add_argument('--use_bidirectional_distill', type=bool, default=True, help='是否使用双向蒸馏')
    parser.add_argument('--distill_weight', type=float, default=0.1, help='蒸馏损失权重')
    parser.add_argument('--log_distill', type=bool, default=True, help='是否记录蒸馏损失')
    parser.add_argument('--fusion_method', type=str, default='sum', help='融合方法：sum或concat')
    
    # 专家合作特有参数
    parser.add_argument('--use_expert_loss', type=bool, default=True, help='是否使用专家分支损失')
    parser.add_argument('--expert_loss_weight', type=float, default=0.3, help='专家分支损失权重')
    parser.add_argument('--use_theta_reg', type=bool, default=True, help='是否使用Theta正则化')
    parser.add_argument('--theta_reg_weight', type=float, default=0.01, help='Theta正则化权重')
    
    # 设备参数
    parser.add_argument('--device', type=str, default='cuda:0', help='设备')
    
    return parser

def get_config_for_stage1():
    """
    获取阶段一（基础迁移）的配置
    """
    parser = get_expert_collaboration_config()
    
    # 阶段一特定配置
    # 使用基础的专家合作机制，不启用高级特性
    parser.set_defaults(
        use_mamba_expert=False,  # 使用简单的MLP作为专家
        class_specific_weights=False,  # 使用全局权重
        use_dynamic_weights=False,  # 不使用动态权重
    )
    
    return parser

def get_config_for_stage2():
    """
    获取阶段二（专家扩展）的配置
    """
    parser = get_expert_collaboration_config()
    
    # 阶段二特定配置
    # 增加专家类型和数量，设计模态特异性专家
    parser.set_defaults(
        use_mamba_expert=True,  # 使用Mamba作为专家
        expert_hidden_dim=256,  # 增大专家隐藏层维度
        expert_output_dim=128,  # 增大专家输出维度
    )
    
    return parser

def get_config_for_stage3():
    """
    获取阶段三（融合优化）的配置
    """
    parser = get_expert_collaboration_config()
    
    # 阶段三特定配置
    # 改进权重优化策略，引入动态权重机制
    parser.set_defaults(
        class_specific_weights=True,  # 使用类别特定权重
        use_dynamic_weights=True,  # 使用动态权重
        use_theta_reg=True,  # 使用Theta正则化
        theta_reg_weight=0.05,  # 增大Theta正则化权重
    )
    
    return parser

def get_config_for_stage4():
    """
    获取阶段四（评估与改进）的配置
    """
    parser = get_expert_collaboration_config()
    
    # 阶段四特定配置
    # 全面评估系统性能，针对瓶颈进行优化
    parser.set_defaults(
        # 综合前三个阶段的最佳配置
        use_mamba_expert=True,
        expert_hidden_dim=256,
        expert_output_dim=128,
        class_specific_weights=True,
        use_dynamic_weights=True,
        use_theta_reg=True,
        theta_reg_weight=0.05,
        distill_weight=0.2,  # 增大蒸馏权重
        expert_loss_weight=0.5,  # 增大专家损失权重
    )
    
    return parser 
import torch
import torch.nn as nn
import torch.nn.functional as F

class ModalExpert(nn.Module):
    """
    单一模态专家分支。
    对单一模态的高层特征进行进一步建模和抽象，
    为后续的多模态融合或专家合作机制提供判别性更强的特征或预测结果。
    可以根据实际需求将MLP替换为Mamba、Transformer等更复杂的结构。
    """
    def __init__(self, input_dim, hidden_dim, output_dim):
        super(ModalExpert, self).__init__()
        # 使用简单的三层感知机作为示例
        self.mlp = nn.Sequential(
            nn.Linear(input_dim, hidden_dim),  # 输入层到隐藏层
            nn.ReLU(),                        # 激活函数
            nn.Linear(hidden_dim, output_dim)  # 隐藏层到输出层
        )

    def forward(self, x):
        """
        前向传播，输入为该模态的特征，输出为预测或高层特征。
        :param x: Tensor, shape=[batch_size, input_dim]
        :return: Tensor, shape=[batch_size, output_dim]
        """
        return self.mlp(x)

class CMOE(nn.Module):
    """
    Cooperation Mixture of Experts (CMOE)：多模态专家协作结构。
    管理多个模态专家分支，每个分支负责处理一种模态。
    """
    def __init__(self, expert_configs):
        """
        初始化CMOE结构。
        :param expert_configs: List[dict]，每个dict包含input_dim, hidden_dim, output_dim，分别对应每个模态专家的结构参数。
        """
        super(CMOE, self).__init__()
        self.num_experts = len(expert_configs)  # 专家（模态）数量
        # 创建每个模态的专家分支
        self.experts = nn.ModuleList([
            ModalExpert(cfg['input_dim'], cfg['hidden_dim'], cfg['output_dim'])
            for cfg in expert_configs
        ])

    def forward(self, modal_inputs):
        """
        前向传播，将每个模态的输入分别送入对应的专家分支。
        :param modal_inputs: List[Tensor]，每个Tensor为一个模态的输入，长度等于专家数。
        :return: List[Tensor]，每个专家的输出，顺序与输入一致。
        """
        assert len(modal_inputs) == self.num_experts, "输入模态数与专家数不一致"
        # 依次将每个模态输入送入对应的专家，得到输出列表
        expert_outputs = [expert(x) for expert, x in zip(self.experts, modal_inputs)]
        return expert_outputs

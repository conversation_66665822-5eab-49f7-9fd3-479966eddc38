import torch
import torch.nn as nn
import torch.nn.functional as F
from trains.subNets import BertTextEncoder
from trains.subNets.transformers_encoder.transformer import TransformerEncoder
from trains.singleTask.model.router import router
from mamba_modal.mamba_block import MambaBlock

class ExpertCollaboration(nn.Module):
    def __init__(self, feature_dim, num_experts=3, num_classes=2):
        super().__init__()
        self.feature_dim = feature_dim
        self.num_experts = num_experts
        self.num_classes = num_classes
        
        # 可学习的置信度张量 (confidence tensor)
        self.confidence = nn.Parameter(torch.ones(num_experts, num_classes))
        
        # 专家特定的投影层
        self.expert_projections = nn.ModuleList([
            nn.Linear(feature_dim, feature_dim) for _ in range(num_experts)
        ])
        
    def forward(self, expert_features):
        # expert_features: 列表，包含各专家输出的特征 [batch_size, feature_dim]
        batch_size = expert_features[0].shape[0]
        
        # 专家输出投影
        projected_features = [
            proj(feat) for proj, feat in zip(self.expert_projections, expert_features)
        ]
        
        # 构建专家输出矩阵 [batch_size, num_experts, feature_dim]
        expert_outputs = torch.stack(projected_features, dim=1)
        
        # 应用置信度张量进行加权
        # 为每个样本复制置信度张量
        confidence = self.confidence.unsqueeze(0).expand(batch_size, -1, -1)  # [batch_size, num_experts, num_classes]
        
        # 加权融合
        weighted_outputs = []
        for i in range(self.num_classes):
            # 提取当前类别的置信度 [batch_size, num_experts]
            class_confidence = confidence[:, :, i]
            
            # 应用softmax归一化
            class_weights = F.softmax(class_confidence, dim=1).unsqueeze(-1)  # [batch_size, num_experts, 1]
            
            # 加权求和
            weighted_sum = torch.sum(expert_outputs * class_weights, dim=1)  # [batch_size, feature_dim]
            weighted_outputs.append(weighted_sum)
        
        # 返回适合MMOE使用的权重格式
        # 对于每个样本，返回一个形状为[3]的权重向量（每个模态一个权重）
        channel_weights = F.softmax(class_confidence, dim=1)  # [batch_size, num_experts]
        
        # 如果是单类别任务（如回归），直接返回第一个输出
        if self.num_classes == 1:
            return weighted_outputs[0], channel_weights
        
        # 否则返回所有加权输出的堆叠
        return torch.stack(weighted_outputs, dim=1), channel_weights

class HierarchicalExpertSystem(nn.Module):
    def __init__(self, feature_dim, hidden_dim=128):
        super().__init__()
        # 低级专家（处理单一模态）
        self.low_level_experts = nn.ModuleList([
            nn.Sequential(
                nn.Linear(feature_dim, hidden_dim),
                nn.LayerNorm(hidden_dim),
                nn.ReLU(),
                nn.Linear(hidden_dim, feature_dim)
            ) for _ in range(3)  # 3个模态
        ])
        
        # 中级专家（处理模态对）
        self.mid_level_experts = nn.ModuleList([
            nn.Sequential(
                nn.Linear(feature_dim*2, hidden_dim),
                nn.LayerNorm(hidden_dim),
                nn.ReLU(),
                nn.Linear(hidden_dim, feature_dim)
            ) for _ in range(3)  # 3个模态对(text+audio, text+video, audio+video)
        ])
        
        # 高级专家（处理全部模态）
        self.high_level_expert = nn.Sequential(
            nn.Linear(feature_dim*3, hidden_dim),
            nn.LayerNorm(hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, feature_dim)
        )
        
        # 专家权重生成器
        self.weight_generator = nn.Sequential(
            nn.Linear(feature_dim*3, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, 7)  # 3个低级+3个中级+1个高级
        )
        
    def forward(self, features):
        # features是一个列表 [text_feat, audio_feat, video_feat]
        
        # 低级专家处理
        low_outputs = [expert(feat) for expert, feat in zip(self.low_level_experts, features)]
        
        # 中级专家处理（模态对）
        pairs = [
            torch.cat([features[0], features[1]], dim=1),  # text+audio
            torch.cat([features[0], features[2]], dim=1),  # text+video
            torch.cat([features[1], features[2]], dim=1)   # audio+video
        ]
        mid_outputs = [expert(pair) for expert, pair in zip(self.mid_level_experts, pairs)]
        
        # 高级专家处理
        all_features = torch.cat(features, dim=1)
        high_output = self.high_level_expert(all_features)
        
        # 生成专家权重
        all_weights = F.softmax(self.weight_generator(all_features), dim=1)
        
        # 加权融合所有专家输出
        all_expert_outputs = low_outputs + mid_outputs + [high_output]
        
        # 初始化融合输出
        fused_output = torch.zeros_like(features[0])
        
        # 加权求和
        for i, expert_out in enumerate(all_expert_outputs):
            fused_output += expert_out * all_weights[:, i:i+1]
            
        return fused_output, all_weights
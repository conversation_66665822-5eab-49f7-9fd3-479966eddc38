1.替换原始的transformer层，使用mamba进行低级特征的提取
    用 SIGMA 模块（它融合了双向Mamba和GRU）替换了原有的 Transformer，
    在不破坏模型整体结构的前提下，升级了其序列特征提取的核心能力，使其拥有更好的长时依赖捕捉能力和更高的运行效率。
2.内部状态门控 (Internal State Gating)（属于1中的一部分）
    三个模态的特征（经过初步的Conv1d投影后）被分别送入各自专属的 sigma_processor
    在 sigma_processor_l 内部，GMambaBlock 的内部状态门控对于序列中的每一个时间步（每一个词、每一帧图像）
    执行以下操作：
    Mamba 自带的门控: 动态计算 Δ (Delta)，实现了内容感知的选择性信息处理。
    我们额外添加的门控: 计算 h1 信号，对 Mamba 处理后的输出进行再次的动态调整和缩放。
    内部状态门控工作在模型的核心处理层，专注于单个模态内部，在时间序列的每一个点上进行精细的、内容感知的信息筛选和动态调整。
    
    实验结果：TEST-(demo) >>  Acc_2: 0.8643  F1_score: 0.8640  Acc_7: 0.4723  MAE: 0.6998  Loss: 0.7002
    Acc_2提升1%，MAE提升1%，F1_score提升1%，Acc_7下降0.5%
3.
    
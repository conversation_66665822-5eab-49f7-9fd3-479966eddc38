"""
ATIO -- All Trains in One
"""
from .singleTask import *
from mamba_modal.DEMO2.DEMO import DEMO
__all__ = ['ATIO']

class ATIO():
    """
    ATIO类：用于根据配置参数动态选择和实例化训练模型。
    主要用于训练脚本中，根据model_name选择不同的模型结构。
    """
    def __init__(self):
        # TRAIN_MAP字典：key为模型名称，value为对应的模型类
        self.TRAIN_MAP = {
            'emoe': EMOE,  # 'emoe'对应EMOE模型
            'demo': DEMO,  # 'demo'对应DEMO模型
        }
    
    def getTrain(self, args):
        """
        根据参数中的'model_name'，返回对应的模型实例。
        args: 配置参数字典，需包含'model_name'字段。
        返回：实例化的模型对象
        """
        return self.TRAIN_MAP[args['model_name']](args)

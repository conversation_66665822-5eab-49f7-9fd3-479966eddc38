# mamba_modules.py

import torch
import torch.nn as nn
import torch.nn.functional as F
from functools import partial
from mamba_ssm.modules.mamba_simple import Mamba

# 注意：VSSBlock_Cross_new 已被重命名为 CrossInteractionBlock 以便更好地融入您的项目。
# 内部的 SS2D_cross_new 也被适配为只处理1D序列的 CrossMamba1D。

class CrossMamba1D(nn.Module):
    def __init__(
            self,
            d_model,
            d_state=16,
            d_conv=4,
            expand=2,
            **kwargs,
    ):
        """
        这是从 FusionMamba 的 SS2D_cross_new 适配而来的核心1D Mamba交互模块。
        它移除了所有2D相关的扫描逻辑，直接处理1D序列。
        """
        super().__init__()
        self.d_model = d_model
        self.d_state = d_state
        self.d_conv = d_conv
        self.expand = expand
        self.d_inner = int(self.expand * self.d_model)

        # 定义两个独立的 Mamba 实例，用于双向交互
        # 一个用于 x1 -> x2 的交互，一个用于 x2 -> x1 的交互
        self.mamba_x1_to_x2 = Mamba(
            d_model=self.d_model,
            d_state=self.d_state,
            d_conv=self.d_conv,
            expand=self.expand,
            **kwargs,
        )
        self.mamba_x2_to_x1 = Mamba(
            d_model=self.d_model,
            d_state=self.d_state,
            d_conv=self.d_conv,
            expand=self.expand,
            **kwargs,
        )
        self.norm = nn.LayerNorm(d_model)

    def forward(self, x1, x2):
        """
        输入:
            x1: 第一个模态的序列, shape: (B, L, D)
            x2: 第二个模态的序列, shape: (B, L, D)
        输出:
            out1: 被 x2 增强后的 x1
            out2: 被 x1 增强后的 x2
        """
        # 为了实现交叉，我们将一个模态的输出作为另一个模态的“增强信号”
        # mamba的输出和输入shape一致 (B, L, D)
        x1_enhanced_by_x2 = self.mamba_x2_to_x1(x2)
        x2_enhanced_by_x1 = self.mamba_x1_to_x2(x1)

        # 使用残差连接将增强信号加到原始信号上
        out1 = self.norm(x1 + x1_enhanced_by_x2)
        out2 = self.norm(x2 + x2_enhanced_by_x1)

        return out1, out2


class CrossInteractionBlock(nn.Module):
    """
    这是从 FusionMamba 的 VSSBlock_Cross_new 适配而来的高层封装。
    """
    def __init__(
            self,
            hidden_dim: int,
            d_state: int = 16,
            **kwargs,
    ):
        super().__init__()
        self.hidden_dim = hidden_dim
        self.ln_1 = nn.LayerNorm(hidden_dim)
        # 内部使用我们新适配的 CrossMamba1D 模块
        self.ssm = CrossMamba1D(d_model=hidden_dim, d_state=d_state, **kwargs)

    def forward(self, x1: torch.Tensor, x2: torch.Tensor):
        # 预归一化
        x1_ln = self.ln_1(x1)
        x2_ln = self.ln_1(x2)
        # 调用核心Mamba交互模块
        x1_out, x2_out = self.ssm(x1_ln, x2_ln)
        return x1_out, x2_out
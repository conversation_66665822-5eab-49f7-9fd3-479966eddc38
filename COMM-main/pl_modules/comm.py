from torch import nn
import torch
from collections import OrderedDict
from typing import Dict, List
# Local imports
from pl_modules.base import BaseModel
from losses.comm_loss import CoMMLoss
from models.mmfusion import MMFusion

class CoMM(BaseModel):
    """ Contrastive MultiModal learning allowing the communication between modalities 
    in a single multimodal space [1].
    
    它通过一个单一的多模态编码器对一对多模态数据进行编码，并输出一对表示。
    It encodes a pair of mulitmodal data and outputs a pair of representations through
    a single multimodal encoder.

    [1] What to align in multimodal contrastive learning, Dufumier & Castillo-Navarro et al., ICLR 2025
    """

    def __init__(self,
                 encoder: MMFusion,
                 projection: nn.Module,
                 optim_kwargs: Dict,
                 loss_kwargs: Dict):
        """
        Args:
            encoder: 多模态融合编码器
            projection: 用于投影到潜在空间的 MLP 投影头
            optim_kwargs: 优化的超参数
            loss_kwargs: CoMM 损失函数的超参数
        """
        super(CoMM, self).__init__(optim_kwargs)

        # 创建多模态融合编码器, 这是CoMM模型的核心组件, 负责融合来自不同模态的特征。
        self.encoder = encoder

        # 构建投影头 (通常是一个MLP), 用于将融合后的特征投影到一个用于计算对比损失的潜在空间。
        self.head = projection

        # 构建 CoMM 损失函数, 这是训练的核心驱动力。
        self.loss = CoMMLoss(**loss_kwargs)


    @staticmethod
    def _build_mlp(in_dim, mlp_dim, out_dim):
        """ 一个静态辅助方法，用于构建一个标准的3层MLP """
        return nn.Sequential(OrderedDict([
            ("layer1", nn.Linear(in_dim, mlp_dim)),
            ("bn1", nn.SyncBatchNorm(mlp_dim)),
            ("relu1", nn.ReLU(inplace=True)),
            ("layer2", nn.Linear(mlp_dim, mlp_dim)),
            ("bn2", nn.SyncBatchNorm(mlp_dim)),
            ("relu2", nn.ReLU(inplace=True)),
            ("layer3", nn.Linear(mlp_dim, out_dim)),
        ]))


    def forward(self, x1: List[torch.Tensor], x2: List[torch.Tensor]):
        """
        定义了模型的前向传播逻辑。
        x1 和 x2 是同一个多模态数据的两个不同“增强”视图。
        """
        # 1. 为当前数据的所有模态生成所有可能的组合掩码
        #    例如，对于双模态，它会生成 [仅模态1], [仅模态2], [模态1+模态2] 三种情况
        all_masks = self.gen_all_possible_masks(len(x1))
        
        # 2. 将两种视图的数据和所有掩码传递给编码器，得到所有模态组合下的特征表示
        #    z1 和 z2 都会是一个列表，包含多个模态组合的特征
        z1 = self.encoder(x1, mask_modalities=all_masks)
        z2 = self.encoder(x2, mask_modalities=all_masks)

        # 3. 将编码器输出的每个特征都通过投影头进行映射
        z1 = [self.head(z) for z in z1]
        z2 = [self.head(z) for z in z2]

        # 4. 返回两个视图的投影后特征，用于后续的损失计算
        return {'aug1_embed': z1,
                'aug2_embed': z2,
                "prototype": -1}
    

    def gen_all_possible_masks(self, n_mod: int):
        """
        生成所有可能的模态组合掩码，这是CoMM思想的关键实现。
        它不仅考虑了每个单独的模态，还考虑了所有模态的完全融合。

        :param n_mod: int, 模态的数量
        :return: 一个布尔掩码的列表，其中每个掩码代表一种模态组合
        Examples:
        *   当 n_mod==2 (例如图像+文本):
            生成的掩码为 [[True, False], [False, True], [True, True]]
            分别代表 [仅图像], [仅文本], [图像+文本]
        *   当 n_mod == 3:
            生成的掩码为 [[True, False, False], [False, True, False], [False, False, True], [True, True, True]]
        """
        masks = []
        # 首先，为每个单模态创建一个掩码
        for L in range(n_mod):
            mask = [s == L for s in range(n_mod)]
            masks.append(mask)
        # 最后，添加一个包含所有模态的掩码
        masks.append([True for _ in range(n_mod)])
        return masks
    
    
    def extract_features(self, loader: torch.utils.data.DataLoader, **kwargs):
        """
           从编码器中提取多模态特征。
           这是一个辅助函数，主要用于评估阶段（例如线性探测）。
           它在推理模式下运行，以高效地从数据加载器中提取所有特征和标签。
           Args:
                loader: 用于提供 (X, y) 元组的数据加载器
                kwargs: 传递给 `encoder.forward()` 的额外参数
           Returns: 
                一个元组 (Z, y)，包含提取的特征和对应的标签
        """
        X, y = [], []
        for X_, y_ in loader:
            if isinstance(X_, torch.Tensor): # 如果只有一个模态，需要将其转换为列表
                X_ = [X_]
            # 将数据移动到正确的设备上
            X_ = [x.to(self.device) if isinstance(x, torch.Tensor) else x for x in X_]
            y_ = y_.to(self.device)
            # 使用推理模式，不计算梯度以节省内存和时间
            with torch.inference_mode():
                # 计算输出
                output = self.encoder(X_, **kwargs)
                X.extend(output.view(len(output), -1).detach().cpu())
                y.extend(y_.detach().cpu())
        # 清空CUDA缓存
        torch.cuda.empty_cache()
        # 将列表堆叠成张量并返回
        return torch.stack(X, dim=0).to(self.device), torch.stack(y, dim=0).to(self.device)

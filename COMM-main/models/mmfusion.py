import torch
import torch.nn as nn
import numpy as np
from typing import List, Optional, Union
from einops import repeat
from collections import OrderedDict
# Local import
#from models.mlp import MLP
from mlp import MLP

class QuickGELU(nn.Module):
    """GELU激活函数的一个快速近似实现"""
    def forward(self, x: torch.Tensor):
        return x * torch.sigmoid(1.702 * x)


class ResidualCrossAttentionBlock(nn.Module):
    """
    带有残差连接的交叉注意力模块。
    用于在两个不同的输入序列之间进行注意力计算。
    """
    def __init__(self, d_model: int, n_heads: int,
                 add_bias_kv: bool = False,
                 dropout: float = 0.,
                 batch_first: bool = False):
        super().__init__()

        # PyTorch内置的多头注意力模块
        self.attn = nn.MultiheadAttention(d_model, n_heads, add_bias_kv=add_bias_kv,
                                          dropout=dropout,  batch_first=batch_first)
        # 交叉注意力的两个输入x和y分别需要独立的层归一化
        self.ln_1x = nn.LayerNorm(d_model)
        self.ln_1y = nn.LayerNorm(d_model)
        # 注意力计算后的前馈网络 (MLP)
        self.mlp = nn.Sequential(OrderedDict([
            ("c_fc", nn.Linear(d_model, d_model * 4)),
            ("gelu", QuickGELU()),
            ("c_proj", nn.Linear(d_model * 4, d_model))
        ]))
        self.ln_2 = nn.LayerNorm(d_model)

    def attention(self, x: torch.Tensor, y: torch.Tensor, key_padding_mask: torch.Tensor = None,
                  attn_mask: torch.Tensor = None):
        # 核心的交叉注意力操作：
        # 查询(Query) 来自 x, 键(Key)和值(Value) 来自 y
        return self.attn(x, y, y, need_weights=False, key_padding_mask=key_padding_mask, attn_mask=attn_mask)[0]

    def forward(self, x: torch.Tensor, y: torch.Tensor, key_padding_mask: torch.Tensor = None,
                attn_mask: torch.Tensor = None):
        # 残差连接：输入x + 注意力输出
        x = x + self.attention(self.ln_1x(x), self.ln_1y(y), key_padding_mask=key_padding_mask, attn_mask=attn_mask)
        # 第二个残差连接：上一步结果 + MLP输出
        x = x + self.mlp(self.ln_2(x))
        return x


class ResidualAttentionBlock(nn.Module):
    """带有残差连接的自注意力模块。"""
    def __init__(self, d_model: int, n_head: int,
                 add_bias_kv: bool = False,
                 dropout: float = 0.,
                 batch_first: bool = False):
        super().__init__()
        self.attn = nn.MultiheadAttention(d_model, n_head, add_bias_kv=add_bias_kv,
                                          dropout=dropout,  batch_first=batch_first)
        self.ln_1 = nn.LayerNorm(d_model)
        self.mlp = nn.Sequential(OrderedDict([
            ("c_fc", nn.Linear(d_model, d_model * 4)),
            ("gelu", QuickGELU()),
            ("c_proj", nn.Linear(d_model * 4, d_model))
        ]))
        self.ln_2 = nn.LayerNorm(d_model)

    def attention(self, x: torch.Tensor, key_padding_mask: torch.Tensor = None):
        # 核心的自注意力操作：
        # 查询(Query), 键(Key), 值(Value) 全部来自同一个输入 x
        return self.attn(x.clone(), x, x, need_weights=False, key_padding_mask=key_padding_mask)[0]

    def forward(self, x: torch.Tensor, key_padding_mask: torch.Tensor = None):
        # 残差连接
        x = x + self.attention(self.ln_1(x), key_padding_mask=key_padding_mask)
        x = x + self.mlp(self.ln_2(x))
        return x


class FusionTransformer(nn.Module):
    """
    使用注意力机制来融合多种模态特征的 Transformer。
    输入形状: [(N, L1, E), (N, L2, E), ...], 输出形状: (N, E)
    它支持两种主要的融合策略:
        - "concat": 将所有模态的 token 序列拼接在一起，然后应用一个标准的自注意力模块。
        - "x-attn": 在两组 token 之间进行交叉注意力计算，然后将结果拼接。
    可以为每个模态的 token 序列提供一个注意力掩码。
    """
    def __init__(self, width: int,
                 n_heads: int,
                 n_layers: int,
                 fusion: str = "concat",
                 pool: str = "cls",
                 add_bias_kv: bool = False,
                 dropout: float = 0.,
                 batch_first: bool = True):
        """
        :param width: 嵌入向量的维度 (embedding size)
        :param n_heads: 多头注意力中的头数
        :param n_layers: 注意力模块的层数
        :param fusion: 融合策略, "concat" 或 "x-attn"
        :param pool: 池化策略, "cls" (使用[CLS] token) 或 "mean" (对所有token取平均)
        :param add_bias_kv: 如果为True, 为键和值的序列添加偏置项
        :param dropout: 注意力权重的dropout概率
        :param batch_first: 如果为True, 输入张量的形状为 (batch, tokens, features), 否则为 (tokens, batch, features)
        """
        super().__init__()

        self.fusion = fusion
        self.width = width
        self.layers = n_layers
        self.norm = nn.LayerNorm(width)
        # token维度在张量中的位置 (0 或 1)
        self.token_dim = 1 if batch_first else 0
        self.pool = pool
        # 如果使用 'cls' 池化，则创建一个可学习的 [CLS] token
        self.cls_token = nn.Parameter(torch.randn(1, 1, width)) if self.pool == "cls" else None
        if fusion == "concat":
            # "concat" 模式下，融合模块由一系列自注意力块组成
            self.resblocks = nn.Sequential(*[
                ResidualAttentionBlock(width, n_heads, add_bias_kv=add_bias_kv,
                                       dropout=dropout, batch_first=batch_first)
                for _ in range(n_layers)])
        elif fusion == "x-attn":
            # "x-attn" 模式下，融合模块由两个交叉注意力块序列组成 (A->B 和 B->A)
            self.resblocks = [
                nn.Sequential(*[
                    ResidualCrossAttentionBlock(width, n_heads, add_bias_kv=add_bias_kv,
                                                dropout=dropout, batch_first=batch_first)
                    for _ in range(n_layers)])
                for _ in range(2)]
        else:
            raise ValueError("Unknown fusion %s" % fusion)
        self.initialize()

    def initialize(self):
        """初始化模型参数，有助于稳定训练"""
        proj_std = (self.width ** -0.5) * ((2 * self.layers) ** -0.5)
        attn_std = self.width ** -0.5
        fc_std = (2 * self.width) ** -0.5
        for block in self.resblocks:
            nn.init.normal_(block.attn.in_proj_weight, std=attn_std)
            nn.init.normal_(block.attn.out_proj.weight, std=proj_std)
            nn.init.normal_(block.mlp.c_fc.weight, std=fc_std)
            nn.init.normal_(block.mlp.c_proj.weight, std=proj_std)

    def forward(self, x: List[torch.Tensor], key_padding_mask: List[torch.Tensor] = None):
        """
        :param x: 一个包含多个模态 token 序列的列表
        :param key_padding_mask: 布尔类型的掩码列表。`True` 表示该 token 不参与注意力计算。
        :return: 融合后的特征向量
        """
        # --- "concat" 融合策略 ---
        if self.fusion == "concat":
            # 1. 在 token 维度上拼接所有模态的 token 序列
            x = torch.cat(x, dim=self.token_dim)
            if key_padding_mask is not None:
                key_padding_mask = torch.cat(key_padding_mask, dim=self.token_dim)
            
            # 2. 如果使用 'cls' 池化，在序列的开头拼接上 [CLS] token
            if self.pool == "cls": 
                cls_token = repeat(self.cls_token, '1 1 d -> b 1 d', b=x.shape[0])
                x = torch.cat((cls_token, x), dim=self.token_dim)
                if key_padding_mask is not None:
                    # [CLS] token 自身不需要被 mask 掉
                    key_padding_mask = torch.cat(
                        (torch.zeros_like(cls_token[:, :, 0]), key_padding_mask), dim=self.token_dim)

            if key_padding_mask is not None:
                # 将布尔掩码转换为浮点数掩码，以便输入到注意力模块
                key_padding_mask = key_padding_mask.masked_fill(key_padding_mask.bool(), float("-inf")).float()

            # 3. 将拼接后的长序列通过一系列自注意力模块
            for layer in self.resblocks:
                x = layer(x, key_padding_mask=key_padding_mask)

            # 4. 最后进行一次层归一化
            x = self.norm(x)

            # 5. 根据池化策略，从 token 序列中提取最终的特征向量
            if self.pool == "cls":
                # 提取 [CLS] token 的输出作为最终表示
                x = x[:, 0] if self.token_dim == 1 else x[0]
            else:
                # 对所有 token 的输出取平均作为最终表示
                x = x.mean(dim=self.token_dim)
            return x
            
        # --- "x-attn" 融合策略 ---
        elif self.fusion == "x-attn":
            if self.pool == "cls":
                raise ValueError("交叉注意力模式下只实现了 'mean' 池化")
            if len(x) != 2:
                raise ValueError("交叉注意力目前只支持两种模态")
            if key_padding_mask is not None:
                raise NotImplementedError()
            
            # 1. 分别计算 x1->x2 和 x2->x1 的交叉注意力
            x1, x2 = x
            x = torch.cat([self.resblocks[0](x1, x2, key_padding_mask),
                           self.resblocks[1](x2, x1, key_padding_mask)], dim=self.token_dim)
            # 2. 对交叉注意力的结果进行层归一化和平均池化
            x = self.norm(x).mean(dim=self.token_dim)
            return x


class MMFusion(nn.Module):
    """
    一个通用的多模态融合模型，使用 FusionTransformer 作为核心融合引擎。
    它负责调用各个单模态编码器，并通过适配器将输出转换为 token 序列，最后送入 FusionTransformer。
    """
    def __init__(self,
                 encoders: List[nn.Module],
                 input_adapters: List[nn.Module],
                 embed_dim: int = 512,
                 fusion: str = "concat",
                 pool: str = "cls",
                 n_heads: int = 8,
                 n_layers: int = 1,
                 add_bias_kv: bool = False,
                 dropout: float = 0.):
        """
        :param encoders: 包含各个模态编码器（如CNN, Transformer, MLP等）的列表
        :param input_adapters: 包含各个模态适配器（可为None）的列表。适配器用于将编码器输出转换为token。
        :param embed_dim: 嵌入维度
        :param fusion: 融合策略, "concat" 或 "x-attn"
        :param pool: 池化策略, "cls" 或 "mean"
        :param n_heads: 多头注意力中的头数
        :param n_layers: 融合模块中的注意力层数
        :param add_bias_kv: 如果为True, 在键/值映射中添加偏置项
        :param dropout: 注意力矩阵的dropout概率
        """
        super().__init__()
        assert len(encoders) == len(input_adapters), "每个编码器必须对应一个适配器（可以是None）"
        assert pool in {'cls', 'mean'}, "池化类型必须是 'cls' 或 'mean'"
        self.input_adapters = nn.ModuleList(input_adapters)
        self.encoders = nn.ModuleList(encoders)
        self.pool = pool
        self.num_modalities = len(self.encoders)
        # 核心组件：实例化一个 FusionTransformer
        self.fusion_transformer = FusionTransformer(embed_dim, n_heads, n_layers,
                                                    fusion, pool, add_bias_kv, dropout,
                                                    batch_first=True)

    def forward(self, x: List[torch.Tensor],
                mask_modalities: Optional[Union[List[bool], List[List[bool]]]] = None):
        """
        定义了从原始输入到最终融合特征的完整流程。
        :param x: 包含多个模态输入张量的列表
        :param mask_modalities: 模态掩码。这是与 CoMM 模块交互的关键。
            如果它是一个列表的列表（例如 [[T,F],[F,T],[T,T]]），则会根据每个子掩码计算一个融合结果。
        :return: 一个融合后的特征向量 z，或者一个包含多个 z 的列表。
        """
        list_mask_mod = None
        # --- 处理 CoMM 传递过来的复杂掩码 ---
        if mask_modalities is None:
            # 如果没有提供掩码，默认使用所有模态
            mask_modalities = self.num_modalities * [True]
        elif isinstance(mask_modalities, list) and len(mask_modalities)>0 and isinstance(mask_modalities[0], list):
            # 如果是列表的列表，说明需要为多个模态组合分别计算结果
            list_mask_mod = mask_modalities
            # 先假设使用所有模态来完成第一阶段的编码和适配
            mask_modalities = self.num_modalities * [True]

        assert len(mask_modalities) == self.num_modalities, (
            f"掩码尺寸与模态数量不匹配: {len(mask_modalities)} != {self.num_modalities}")

        num_modalities = sum(mask_modalities)
        assert len(x) == num_modalities, (
                f"输入数量不正确: {len(x)} != {num_modalities}")

        # 根据掩码选择需要使用的编码器和适配器
        encoders = [enc for (enc, m) in zip(self.encoders, mask_modalities) if m]
        input_adapters = [adapter for (adapter, m) in zip(self.input_adapters, mask_modalities) if m]
        attn_mask = []

        # --- 流程第1步: 独立编码每个模态 ---
        # 创建一个列表 z 用于存储每个模态编码后的特征
        # 创建一个列表 attn_mask 用于存储每个模态对应的注意力掩码
        z = []
        attn_mask = []
        # 遍历每一个模态的编码器(enc)和其对应的输入数据(xi)
        for (enc, xi) in zip(encoders, x):
            # 调用特定模态的编码器（如ViT, BERT）进行特征提取
            embedding = enc(xi)
            attn_mask_ = None
            # 特殊处理：某些编码器（特别是HuggingFace的Transformers）返回一个字典
            # 而不是一个单纯的张量。我们需要从中解析出真正的特征和注意力掩码。
            if isinstance(embedding, dict):  # 编码器可能会同时返回特征和注意力掩码
                # 提取注意力掩码，用于在后续的注意力计算中忽略填充（padding）部分
                attn_mask_ = embedding["attention_mask"]
                # 提取真正的 token 嵌入特征
                embedding = embedding["token_embeddings"]
            # 将提取出的特征和掩码分别存入列表
            z.append(embedding)
            attn_mask.append(attn_mask_)

        # --- 流程第2步: 将编码后的特征转换为Token序列 ---
        latent_tokens = [adapter(zi) if adapter is not None else zi
                         for (adapter, zi) in zip(input_adapters, z)]
        attn_mask = [attn_mask_ if attn_mask_ is not None else torch.zeros_like(zi[:,:,0]).bool()
                     for (attn_mask_, zi) in zip(attn_mask, latent_tokens)]
        
        # --- 流程第3步: 送入 FusionTransformer 进行融合 ---
        if list_mask_mod is None:
            # 情况A: 只需计算一个融合结果
            z = self.fusion_transformer(latent_tokens, key_padding_mask=attn_mask)
        else:
            # 情况B: 需要为 CoMM 的每个模态组合分别计算融合结果
            z = []
            for mask_mod in list_mask_mod:
                # 根据当前的子掩码，从所有 token 序列中选择需要的模态
                latent_tokens_ = [z for (z, m) in zip(latent_tokens, mask_mod) if m]
                attn_mask_ = [attn for (attn, m) in zip(attn_mask, mask_mod) if m]
                # 将选中的 token 序列送入融合器
                z.append(self.fusion_transformer(latent_tokens_))
        return z

    def encode_single_mod(self, x: torch.Tensor, mod: int):
        """一个辅助函数，用于只编码单个指定的模态"""
        assert 0 <= mod < self.num_modalities, "错误的输入模态索引"
        return self.encoders[mod](x)


class LinearFusion(nn.Module):
    """
    一个简化的多模态融合模型，使用简单的线性层进行融合。
    比 MMFusion 更轻量，但表达能力较弱。
    """
    def __init__(self,
                 encoders: List[nn.Module],
                 mod_dims: List[int],
                 embed_dim: int = 512,
                 **kwargs):
        super().__init__()
        self.encoders = nn.ModuleList(encoders)
        self.mod_dims = mod_dims
        assert len(self.mod_dims) == len(self.encoders)
        self.embed_dim = embed_dim
        self.num_modalities = len(self.encoders)
        # 为每个单模态创建一个独立的线性投影层
        self.projectors = nn.ModuleList([nn.Linear(mod_dim, embed_dim) for mod_dim in mod_dims])
        # 为所有模态拼接后的特征创建一个总的线性投影层
        self.head_projector = nn.Linear(int(sum(mod_dims)), embed_dim)

    def forward(self, x: List[torch.Tensor],
                mask_modalities: Optional[Union[List[bool], List[List[bool]]]] = None):

        list_mask_mod = None
        if mask_modalities is None:
            mask_modalities = self.num_modalities * [True]
        elif isinstance(mask_modalities, list) and len(mask_modalities)>0 and isinstance(mask_modalities[0], list):
            list_mask_mod = mask_modalities
            mask_modalities = self.num_modalities * [True]
        assert len(mask_modalities) == self.num_modalities, (
            f"Mask size does not match `num_modalities`: {len(mask_modalities)} != {self.num_modalities}")
        num_modalities = sum(mask_modalities)
        assert len(x) == num_modalities, (
                f"Incorrect number of inputs: {len(x)} != {num_modalities}")

        # 1. 独立编码
        encoders = [enc for (enc, m) in zip(self.encoders, mask_modalities) if m]
        Z = [enc(xi) for enc, xi in zip(encoders, x)]
        
        # 2. 根据掩码进行融合
        if list_mask_mod is not None:
            Z_ = []
            for mask_mod in list_mask_mod:
                Z_.append(self.get_common_embedding(Z, mask_mod))
            return Z_
        return self.get_common_embedding(Z, mask_modalities)

    def get_common_embedding(self, z: List[torch.Tensor], mask_modalities: List[bool]):
        """根据掩码分发到不同的投影层"""
        if np.sum(mask_modalities) == 1:
            # 如果只有一个模态，使用其独立的投影层
            idx = int(np.nonzero(mask_modalities)[0][0])
            return self.projectors[idx](z[idx])
        elif np.sum(mask_modalities) == 2:
            # 如果有两个模态，先拼接再通过总的投影层
            return self.head_projector(torch.cat(z, dim=-1))
        raise NotImplementedError()


class MLPFusion(nn.Module):
    """
    与 LinearFusion 类似，但使用非线性的 MLP 代替线性层，表达能力更强。
    """
    def __init__(self,
                 encoders: List[nn.Module],
                 mod_dims: List[int],
                 embed_dim: int = 512,
                 **kwargs):
        super().__init__()
        self.encoders = nn.ModuleList(encoders)
        self.mod_dims = mod_dims
        assert len(self.mod_dims) == len(self.encoders)
        self.embed_dim = embed_dim
        self.num_modalities = len(self.encoders)
        # 为每个单模态创建一个独立的非线性 MLP 投影头
        self.projectors = nn.ModuleList([MLP(mod_dim, embed_dim, embed_dim) for mod_dim in mod_dims])
        # 为所有模态拼接后的特征创建一个总的非线性 MLP 投影头
        self.head_projector = MLP(int(sum(mod_dims)), embed_dim, embed_dim)

    def forward(self, x: List[torch.Tensor],
                mask_modalities: Optional[Union[List[bool], List[List[bool]]]] = None):

        list_mask_mod = None
        if mask_modalities is None:
            mask_modalities = self.num_modalities * [True]
        elif isinstance(mask_modalities, list) and len(mask_modalities)>0 and isinstance(mask_modalities[0], list):
            list_mask_mod = mask_modalities
            mask_modalities = self.num_modalities * [True]
        assert len(mask_modalities) == self.num_modalities, (
            f"Mask size does not match `num_modalities`: {len(mask_modalities)} != {self.num_modalities}")
        num_modalities = sum(mask_modalities)
        assert len(x) == num_modalities, (
                f"Incorrect number of inputs: {len(x)} != {num_modalities}")

        encoders = [enc for (enc, m) in zip(self.encoders, mask_modalities) if m]
        Z = [enc(xi) for enc, xi in zip(encoders, x)]
        if list_mask_mod is not None:
            Z_ = []
            for mask_mod in list_mask_mod:
                Z_.append(self.get_common_embedding(Z, mask_mod))
            return Z_
        return self.get_common_embedding(Z, mask_modalities)

    def get_common_embedding(self, z: List[torch.Tensor], mask_modalities: List[bool]):
        """根据掩码分发到不同的 MLP 投影头"""
        if np.sum(mask_modalities) == 1:
            # 如果只有一个模态，使用其独立的 MLP
            idx = int(np.nonzero(mask_modalities)[0][0])
            return self.projectors[idx](z[idx])
        elif np.sum(mask_modalities) == 2:
            # 如果有两个模态，先拼接再通过总的 MLP
            return self.head_projector(torch.cat(z, dim=-1))
        raise NotImplementedError()


if __name__ == "__main__":
    # --- 一个用于快速测试和调试的小脚本 ---
    width = 10
    batch = 3
    fusion = FusionTransformer(width, 2, 2)
    x = [torch.randn((batch, 2, width)), torch.randn((batch, 3, width))]
    # preserve modality 1
    mask = [torch.ones((batch, 2)).bool(), torch.ones((batch, 3)).bool()]
    print(fusion(x, mask))
    print(fusion([x[1]]))




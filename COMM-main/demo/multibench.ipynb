{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# CoMM on MultiBench dataset"]}, {"cell_type": "markdown", "metadata": {}, "source": ["This notebook will show how to use CoMM on the [MultiBench dataset](https://github.com/pliang279/MultiBench) (see Table 1 in [our paper](https://arxiv.org/abs/2409.07402)). "]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Packages install and loading\n", "\n", "We start by installing and loading the required packages for this notebook:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%pip install torch\n", "%pip install omegaconf\n", "%pip install hydra-core\n", "%pip install pytorch-lightning\n", "%pip install scikit-learn\n", "%pip install torchvision\n", "%pip install tensorboard\n", "%pip install pandas\n", "%pip install einops\n", "%pip install matplotlib\n", "%pip install gdown"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import sys\n", "sys.path.append(\"../\")\n", "import numpy as np\n", "import torch\n", "from sklearn.linear_model import LogisticRegressionCV\n", "from dataset.multibench import MultiBenchDataModule\n", "from pytorch_lightning import Trainer\n", "from pl_modules.comm import CoMM\n", "from models.mmfusion import MMFusion\n", "from models.transformer import Transformer\n", "import warnings"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["torch.manual_seed(45) # for reproducibility\n", "np.random.seed(45) \n", "warnings.filterwarnings(\"ignore\", category=UserWarning) # avoids sklearn warnings"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Load the data "]}, {"cell_type": "markdown", "metadata": {}, "source": ["MultiBench consists in 15 preprocessed datasets with predefined train/val/test splits. \n", "\n", "In this notebook, we will focus on 3 datasets:\n", "\n", "- MOSI, a dataset for sentiment analysis in videos (2199 samples)\n", "- UR-FUNNY, a dataset to recognize humoristic videos (16514 samples)\n", "- MUsTARD, a dataset to recognize sarcasm in videos (690 samples)\n", "\n", "\n", "Don't forget to set the path where the data will be automatically downloaded in **dataset/catalog.json** ! "]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["# Load MOSI \n", "data_module_mosi = MultiBenchDataModule(\"mosi\", model=\"CoMM\", \n", "                                        batch_size=64, num_workers=16, \n", "                                        modalities=[\"vision\", \"text\"], \n", "                                        augmentations=\"drop+noise\")\n", "downstream_mosi = MultiBenchDataModule(\"mosi\", model=\"Sup\", \n", "                                        batch_size=64, num_workers=16, \n", "                                        modalities=[\"vision\", \"text\"])\n", "\n", "# Load UR-FUNNY\n", "data_module_humor = MultiBenchDataModule(\"humor\", model=\"CoMM\", \n", "                                        batch_size=64, num_workers=16, \n", "                                        modalities=[\"vision\", \"text\"], \n", "                                        augmentations=\"drop+noise\")\n", "downstream_humor = MultiBenchDataModule(\"humor\", model=\"Sup\", \n", "                                        batch_size=64, num_workers=16, \n", "                                        modalities=[\"vision\", \"text\"])\n", "\n", "# Load MUsTARD\n", "data_module_sarcasm = MultiBenchDataModule(\"sarcasm\", model=\"CoMM\", \n", "                                        batch_size=64, num_workers=16, \n", "                                        modalities=[\"vision\", \"text\"], \n", "                                        augmentations=\"drop+noise\")\n", "downstream_sarcasm = MultiBenchDataModule(\"sarcasm\", model=\"Sup\", \n", "                                        batch_size=64, num_workers=16, \n", "                                        modalities=[\"vision\", \"text\"])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Evaluate CoMM on MultiBench data"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["def classification_scoring(model, data_module, scoring=\"balanced_accuracy\"):\n", "    Z_train, y_train = model.extract_features(data_module.train_dataloader())\n", "    Z_test, y_test = model.extract_features(data_module.test_dataloader())\n", "    linear_model = LogisticRegressionCV(Cs=5, n_jobs=10, scoring=scoring)\n", "    linear_model.fit(Z_train.cpu().detach().numpy(), y_train.cpu().detach().numpy())\n", "    return linear_model.score(Z_test.cpu().detach().numpy(), y_test.cpu().detach().numpy())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### MOSI "]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["comm = CoMM(\n", "    encoder=MMFusion(\n", "        encoders=[ # Handles vision and textual modalities\n", "            Transformer(n_features=20, dim=40, max_seq_length=50, positional_encoding=False), \n", "            Transformer(n_features=300, dim=40, max_seq_length=50, positional_encoding=False), \n", "        ], \n", "        input_adapters=[None, None], # No adapters needed\n", "        embed_dim=40\n", "    ),\n", "    projection=CoMM._build_mlp(40, 512, 256),\n", "    optim_kwargs=dict(lr=1e-3, weight_decay=1e-2),\n", "    loss_kwargs=dict(temperature=0.1)\n", ")"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Trainer will use only 1 of 2 GPUs because it is running inside an interactive / notebook environment. You may try to set `Trainer(devices=2)` but please note that multi-GPU inside interactive / notebook environments is considered experimental and unstable. Your mileage may vary.\n", "GPU available: True (cuda), used: True\n", "TPU available: False, using: 0 TPU cores\n", "IPU available: False, using: 0 IPUs\n", "HPU available: False, using: 0 HPUs\n"]}], "source": ["trainer = Trainer(inference_mode=False, max_epochs=100)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["trainer.fit(comm, datamodule=data_module_mosi)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["score = classification_scoring(comm, downstream_mosi)"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["CoMM accuracy on MOSI=65.06\n"]}], "source": ["print(f\"CoMM accuracy on MOSI={100 * score:.2f}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### UR-FUNNY"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["comm = CoMM(\n", "    encoder=MMFusion(\n", "        encoders=[ # Handles vision and textual modalities\n", "            Transformer(n_features=371, dim=40, max_seq_length=50, positional_encoding=False), \n", "            Transformer(n_features=300, dim=40, max_seq_length=50, positional_encoding=False), \n", "        ], \n", "        input_adapters=[None, None], # No adapters needed\n", "        embed_dim=40\n", "    ),\n", "    projection=CoMM._build_mlp(40, 512, 256),\n", "    optim_kwargs=dict(lr=1e-3, weight_decay=1e-2),\n", "    loss_kwargs=dict(temperature=0.1)\n", ")"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Trainer will use only 1 of 2 GPUs because it is running inside an interactive / notebook environment. You may try to set `Trainer(devices=2)` but please note that multi-GPU inside interactive / notebook environments is considered experimental and unstable. Your mileage may vary.\n", "GPU available: True (cuda), used: True\n", "TPU available: False, using: 0 TPU cores\n", "IPU available: False, using: 0 IPUs\n", "HPU available: False, using: 0 HPUs\n"]}], "source": ["trainer = Trainer(inference_mode=False, max_epochs=100)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["trainer.fit(comm, datamodule=data_module_humor)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["score = classification_scoring(comm, downstream_humor)"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["CoMM accuracy on UR-FUNNY=62.24\n"]}], "source": ["print(f\"CoMM accuracy on UR-FUNNY={100 * score:.2f}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### MUsTARD"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["comm = CoMM(\n", "    encoder=MMFusion(\n", "        encoders=[ # Handles vision and textual modalities\n", "            Transformer(n_features=371, dim=40, max_seq_length=50, positional_encoding=False), \n", "            Transformer(n_features=300, dim=40, max_seq_length=50, positional_encoding=False), \n", "        ], \n", "        input_adapters=[None, None], # No adapters needed\n", "        embed_dim=40\n", "    ),\n", "    projection=CoMM._build_mlp(40, 512, 256),\n", "    optim_kwargs=dict(lr=1e-3, weight_decay=1e-2),\n", "    loss_kwargs=dict(temperature=0.1)\n", ")"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Trainer will use only 1 of 2 GPUs because it is running inside an interactive / notebook environment. You may try to set `Trainer(devices=2)` but please note that multi-GPU inside interactive / notebook environments is considered experimental and unstable. Your mileage may vary.\n", "GPU available: True (cuda), used: True\n", "TPU available: False, using: 0 TPU cores\n", "IPU available: False, using: 0 IPUs\n", "HPU available: False, using: 0 HPUs\n"]}], "source": ["trainer = Trainer(inference_mode=False, max_epochs=100)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["trainer.fit(comm, datamodule=data_module_sarcasm)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["score = classification_scoring(comm, downstream_sarcasm)"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["CoMM accuracy on MUsTARD=64.91\n"]}], "source": ["print(f\"CoMM accuracy on MUsTARD={100 * score:.2f}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "multimodal", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.18"}}, "nbformat": 4, "nbformat_minor": 2}
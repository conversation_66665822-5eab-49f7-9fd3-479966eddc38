CVPR 2025 论文创新点整理

1. Trajectory Mamba: Efficient Attention-Mamba Forecasting Model Based on Selective SSM
   Trajectory Mamba：基于选择性状态空间模型的高效Attention-Mamba轨迹预测模型
   核心创新点：
   - 用Selective SSM模块替代传统自注意力，将O(N²)复杂度降为O(N)，极大提升多目标轨迹预测的效率。
   - 提出联合折线编码策略（Joint Polyline Encoding），提升静态与动态目标间的交互建模能力。
   - 解码器采用Cross-State Space Attention，实现多目标间的全局信息共享与协同推理。
   - 在Argoverse 1/2数据集上实现推理速度、参数量和精度的SOTA。

2. Cross-Modal Interactive Perception Network with Mamba for Lung Tumor Segmentation in PET-CT Images
   基于Mamba的跨模态交互感知网络用于PET-CT肺肿瘤分割
   核心创新点：
   - 首次公开大规模PET-CT肺肿瘤分割数据集PCLT20K（21,930对图像，605名患者，像素级标注）。
   - 提出CIPA网络，包含：
     * 通道校正模块（CRM）：基于Mamba的通道状态空间块，实现跨模态特征相关性建模和噪声过滤。
     * 动态跨模态交互模块（DCIM）：动态集成PET位置信息与CT上下文，实现高效跨模态信息流动。
   - 在大规模基准上显著优于现有SOTA分割方法。

3. AlignMamba: Enhancing Multimodal Mamba with Local and Global Cross-modal Alignment
   AlignMamba：通过局部与全局跨模态对齐增强多模态Mamba
   核心创新点：
   - 提出局部跨模态对齐模块（Local Cross-modal Alignment Module），基于最优传输（Optimal Transport），显式学习不同模态间的token级对应关系。
   - 提出全局跨模态对齐损失（Global Cross-modal Alignment Loss），基于最大均值差异（MMD），隐式约束不同模态分布的一致性。
   - 对齐后的单模态特征输入Mamba主干网络，进一步实现高效的跨模态交互与多模态融合。
   - 兼具高效性与有效性，在完整与不完整多模态融合任务上均取得优异表现。

4. MambaVLT: Time-Evolving Multimodal State Space Model for Vision-Language Tracking
   MambaVLT：用于视觉-语言跟踪的时序演化多模态状态空间模型
   核心创新点：
   - 首次将Mamba状态空间模型引入视觉-语言跟踪任务，利用其高效的时序建模能力，提升多模态时序信息的记忆与利用。
   - 设计时序演化混合状态空间块（Time-evolving Hybrid State Space Block），实现多模态上下文信息的动态建模与融合。
   - 提出选择性局部增强模块（Selective Locality Enhancement Block），提升模型对局部细节的感知能力。
   - 引入模态选择模块（Modality-selection Module），动态调整视觉与语言参考的权重，缓解单一模态歧义。
   - 在多个视觉-语言跟踪基准上取得SOTA表现。

5. MambaVerse: State Space Models Meet Multimodal Foundation Models
   MambaVerse：状态空间模型遇上多模态基础模型
   核心创新点：
   - 首次系统性地将Mamba状态空间模型与多模态基础模型（如视觉-语言大模型）结合，提出统一的多模态状态空间建模框架。
   - 设计高效的多模态状态空间块（Multimodal State Space Block），实现跨模态信息的高效交互与融合。
   - 支持多种模态（如图像、文本、音频、视频等）灵活接入与协同推理，具备极强的可扩展性。
   - 在多项多模态理解与生成任务上取得SOTA或有竞争力的表现，推动多模态基础模型的高效化与通用化。
   - 论文/代码链接: https://arxiv.org/pdf/2505.18605

6. Video Mamba: Modeling Long Video Sequences with Hierarchical Mamba Networks
   Video Mamba：用分层Mamba网络建模长视频序列
   核心创新点：
   - 提出分层Mamba网络（Hierarchical Mamba Networks），首次将Mamba状态空间模型应用于长视频建模，兼顾全局与局部时序建模能力。
   - 设计局部-全局分层结构，底层捕捉短时局部动态，上层建模长时全局依赖，实现高效且强表达力的视频理解。
   - Mamba模块以线性复杂度处理长序列，显著提升长视频建模的效率和可扩展性。
   - 在多项长视频理解任务（如动作识别、视频问答等）上取得SOTA或有竞争力的表现。
   - 论文/代码链接: https://arxiv.org/pdf/2503.02597
